<template>
    <div class="check-item">
        <div class="c1">
            <h2>{{ keyNum }}.{{ checkData.checkTip }}</h2>
            <ul class="c1ul">
                <li>
                    <v-radio
                        class="radio"
                        v-model="val"
                        value="1"
                        @change="change"
                        ><span>是</span></v-radio
                    >
                </li>
                <li>
                    <v-radio
                        class="radio"
                        v-model="val"
                        value="0"
                        @change="change"
                        ><span>否</span></v-radio
                    >
                </li>
            </ul>
        </div>
        <div class="c2" v-show="showPicList">
            <h2>您可以拍下相关照片</h2>
            <ul>
                <li v-for="(data, index) in checkPhotos" :key="'i' + index">
                    <!-- 视频文件：显示默认缩略图和播放按钮 -->
                    <template v-if="data.imgFileId && data.imgFileUrl">
                        <img
                            class="i1"
                            :src="data.imgFileUrl"
                            alt=""
                            data-who="video"
                        />
                        <div
                            class="i1-play"
                            @click="scanVideo(data.fileUrl, data.imgFileUrl)"
                        ></div>
                    </template>
                    <!-- 普通图片 -->
                    <img
                        class="i1"
                        :src="data.fileUrl"
                        alt=""
                        v-else
                        data-who="img"
                    />
                    <span @click="deleteImg(index)"></span>
                </li>
                <li v-if="checkPhotos && checkPhotos.length < 3">
                    <label @click="SelectImgOrVideoFn"> </label>
                </li>
            </ul>
        </div>
        <div class="select-two" v-show="showSlect">
            <div class="st-mask" @click="closeSelectImg"></div>
            <div class="st-content">
                <div class="st-i">
                    <!-- <div class="st-ic" @click="useCameraFn">
                        <label :for="keyword">
                            <input
                                type="file"
                                :id="keyword"
                                accept="image/*"
                                @change="clickUpload($event)"
                                v-if="!isiOS"
                            />
                            <input
                                type="file"
                                :id="keyword"
                                accept="image/*"
                                @change="clickUpload($event)"
                                v-else
                            />
                        </label>
                        <p class="p1">图片</p>
                    </div> -->
                    <div class="st-ic" @click="YSSC">
                        <img
                            src="../../assets/img/<EMAIL>"
                            height="60"
                            alt=""
                        />
                        <p class="p1">图片</p>
                    </div>
                    <div class="st-ic st-icrt" @click="useVideoFn">
                        <img src="../../assets/img/<EMAIL>" alt="" />
                        <p class="p2">视频</p>
                    </div>
                </div>
            </div>
        </div>
        <div class="vi" v-show="showVideo">
            <div class="vi-mask"></div>
            <div class="vi-content">
                <div class="vic-bg" @click="closeVideoBox"></div>
                <div class="vi-main">
                    <div ref="video"></div>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import vRadio from "../../components/v-radio/radio";
import lrz from "lrz";
import { initCookie, upload, getFileToken } from "../../api/check";
import { closeWebview, selectVideo } from "@/assets/js/until.js";
// import 'dplayer/dist/DPlayer.min.css';
import DPlayer from "dplayer";
import defaultVideoThumb from '../../assets/img/video.jpg';
export default {
    props: ["checkData", "keyword", "checkId", "isModified", "keyNum"],
    data() {
        return {
            // 1:是， 0：否，要传图片, null: 不选择
            val: null,
            // fileId 的数组
            checkPhotos: [],
            // 判断是否按钮显示上传图片
            showPicList: false,
            isiOS: false,
            // 显示（图片和视频的）选择项
            showSlect: false,
            // 显示视频
            showVideo: false,
        };
    },
    created() {
        // 判断移动终端
        var u = navigator.userAgent;
        this.isiOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/); //ios终端
    },
    mounted() {
        this.$loading(false, "");
        this.draftsBox();
        // console.log('check-item checkData', this.checkData);
    },
    methods: {
        testInit(callback) {
            initCookie({ authCode: "11111111" }).then((result) => {
                console.log("initCookie", result);
                if (result.errorCode === "0") {
                    callback();
                }
            });
        },
        // 初始化，网页版不需要认证，直接执行回调
        initCookiefn(callback) {
            // 网页版不需要认证，直接执行业务逻辑
            if (callback && typeof callback === 'function') {
                callback();
            }
        },
        change(v) {
            // console.log('v=', v);
        },
        YSSC() {
            // H5图片选择
            const _this = this;
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = 'image/*';
            input.multiple = true; // 允许多选

            input.onchange = (e) => {
                const files = e.target.files;
                if (!files.length) return;

                _this.$loading(true, "正在上传图片...");

                // 处理所有选中的图片
                let uploadPromises = [];
                for (let i = 0; i < files.length; i++) {
                    uploadPromises.push(_this.uploadSingleImage(files[i]));
                }

                Promise.all(uploadPromises).then(() => {
                    _this.$loading(false, "");
                    _this.showSlect = false;
                    _this.$toast.center('图片上传成功');
                }).catch((error) => {
                    console.error('图片上传失败', error);
                    _this.$loading(false, "");
                    _this.showSlect = false;
                    _this.$toast.center('部分图片上传失败');
                });
            };

            input.click();
        },
        // 上传单张图片
        uploadSingleImage(file) {
            const _this = this;
            return new Promise((resolve, reject) => {
                // 使用 lrz 压缩图片
                lrz(file, {
                    width: 1000,
                    quality: 0.5,
                }).then(function (rst) {
                    return getFileToken();
                }).then((token) => {
                    if (token.errorCode.toString() !== "0") {
                        reject(new Error('获取token失败'));
                        return;
                    }

                    const fd = new FormData();
                    fd.append("file", file);
                    fd.append("fileToken", token.data);

                    return upload(fd);
                }).then((res) => {
                    if (res.errorCode.toString() === "0") {
                        // 图片上传成功，设置为图片格式（不设置imgFileId和imgFileUrl）
                        _this.checkPhotos.push({
                            fileUrl: res.data.fileUrl,
                            fileId: res.data.fileId,
                            // 图片不设置 imgFileId 和 imgFileUrl，这样模板会显示为图片
                        });
                        console.log('📷 图片上传成功:', {
                            fileId: res.data.fileId,
                            fileUrl: res.data.fileUrl,
                            类型: '图片'
                        });
                        resolve(res);
                    } else {
                        reject(new Error(res.value));
                    }
                }).catch((error) => {
                    console.error('单张图片上传失败', error);
                    reject(error);
                });
            });
        },
        clickUpload(e) {
            const _this = this;
            _this.$loading(true, "");
            var files = e.target.files || e.dataTransfer.files;
            // console.log('files', files);
            if (!files.length) return;
            lrz(files[0], {
                width: 1000,
                quality: 0.5,
            }).then(function (rst) {
                // _this.imgList.push(rst.base64);
                _this.uploadFn(rst.file);
                return rst;
            });
        },
        deleteImg(i) {
            // this.imgList.splice(i, 1);
            this.checkPhotos.splice(i, 1);
        },
        // 上传图片
        uploadFn(file) {
            const _this = this;
            getFileToken()
                .then((token) => {
                    console.log("getFileToken=", token);
                    if (token.errorCode.toString() === "0") {
                        const fd = new FormData();
                        fd.append("file", file);
                        fd.append("fileToken", token.data);
                        upload(fd)
                            .then((res) => {
                                console.log("upload res=", res);
                                _this.$loading(false, "");
                                if (res.errorCode.toString() === "0") {
                                    _this.checkPhotos.push({
                                        fileUrl: res.data.fileUrl,
                                        fileId: res.data.fileId,
                                    });
                                } else {
                                    _this.$toast.center(res.value);
                                }
                            })
                            .catch((error) => {
                                _this.$toast.center("接口异常");
                            });
                    } else if (token.errorCode.toString() === "1003") {
                        _this.initCookiefn(_this.uploadFn);
                        // _this.testInit(_this.uploadFn)
                    } else {
                        _this.$toast.center(token.value);
                    }
                })
                .catch((error) => {
                    _this.$toast.center("接口异常");
                });
        },
        // 选择上传 视频 或 图片
        SelectImgOrVideoFn() {
            this.showSlect = true;
        },
        closeSelectImg() {
            this.showSlect = false;
        },
        useCameraFn() {
            // 关闭弹窗
            this.showSlect = false;
        },
        /*
         * H5选择视频并上传
         */
        async useVideoFn() {
            const _this = this;
            try {
                // 使用H5选择视频
                const videoData = await selectVideo();
                console.log('选择视频成功', videoData);

                _this.$loading(true, '正在上传视频...');

                // 获取上传token
                const token = await getFileToken();
                if (token.errorCode.toString() !== "0") {
                    if (token.errorCode.toString() === "1003") {
                        _this.initCookiefn(_this.useVideoFn);
                    } else {
                        _this.$toast.center(token.value);
                    }
                    _this.$loading(false, '');
                    return;
                }

                // 构建上传FormData
                const formData = new FormData();
                formData.append('file', videoData.file);
                formData.append('fileToken', token.data);

                // 上传视频
                let saveUrl = window.location.origin + "/medical-supervision/file/uploadVedio";

                const response = await fetch(saveUrl, {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();
                console.log('🎥 视频上传接口返回结果:', result);
                console.log('🔍 result.data 详细结构:', result.data);
                console.log('🔍 result.data 所有字段:', Object.keys(result.data || {}));

                if (result.errorCode === '0' || result.errorCode === 0) {
                    // 直接使用后端返回的字段，统一使用默认缩略图
                    const videoData = {
                        fileUrl: result.data.fileUrl,
                        fileId: result.data.fileId,
                        imgFileId: -1, // 统一使用默认缩略图标识
                        imgFileUrl: defaultVideoThumb
                    };

                    console.log('🎥 视频上传成功，使用默认缩略图:', {
                        fileId: result.data.fileId,
                        fileUrl: result.data.fileUrl,
                        defaultThumb: defaultVideoThumb
                    });

                    console.log('📋 添加视频前的checkPhotos:', _this.checkPhotos.slice());

                    // 添加到照片列表
                    _this.checkPhotos.push(videoData);

                    console.log('📋 添加视频后的checkPhotos:', _this.checkPhotos.slice());
                    console.log('📋 checkPhotos数组长度:', _this.checkPhotos.length);
                    console.log('📋 当前检查项目信息:', {
                        listId: _this.checkData.listId,
                        checkTip: _this.checkData.checkTip,
                        checkId: _this.checkId,
                        当前选择状态: _this.val,
                        是否需要选择: _this.val === null ? '⚠️需要选择是/否' : '✅已选择'
                    });

                    // 检查 watch 监听器是否会被触发
                    console.log('🔍 即将触发 checkPhotos watch 监听器...');

                    // 如果用户还没有选择"是"或"否"，给出明确提示
                    if (_this.val === null) {
                        console.log('⚠️⚠️⚠️ 重要提示：视频上传成功，但需要选择"是"或"否"才能保存！');
                        _this.$toast.center('视频上传成功，请选择"是"或"否"');
                    }

                    _this.$toast.center('视频上传成功');
                } else {
                    _this.$toast.center(result.value || '上传失败');
                }

                _this.$loading(false, '');
                _this.showSlect = false;
            } catch (error) {
                console.error('视频选择或上传失败', error);
                _this.$loading(false, '');
                _this.showSlect = false;
                if (error.message !== '用户取消选择') {
                    _this.$toast.center('视频处理失败');
                }
            }
        },
        // 草稿箱修改
        draftsBox() {
            // console.log('this.isModified=========', this.isModified);
            if (this.isModified) {
                this.val = this.checkData.checkStatus;
                if (this.checkData.checkPhotos !== "") {
                    const checkPhotos = JSON.parse(this.checkData.checkPhotos);
                    console.log('🔍 草稿箱加载检查项目照片数据:', checkPhotos);

                    if (checkPhotos && checkPhotos.length > 0) {
                        this.checkPhotos = checkPhotos.map((file) => {
                            // 区分图片和视频：通过文件扩展名判断
                            let fileData;
                            const fileUrl = file.fileUrl || '';
                            const isVideo = /\.(mp4|avi|mov|wmv|flv|webm|mkv)$/i.test(fileUrl);

                            if (file.imgFileId !== undefined || isVideo) {
                                // 视频文件：使用默认缩略图
                                fileData = {
                                    fileId: file.fileId,
                                    fileUrl: file.fileUrl,
                                    imgFileId: -1, // 视频使用默认缩略图标识
                                    imgFileUrl: defaultVideoThumb
                                };
                                console.log('📁 处理检查项目视频文件:', {
                                    fileId: file.fileId,
                                    fileUrl: file.fileUrl,
                                    判断依据: file.imgFileId !== undefined ? 'imgFileId字段' : '文件扩展名'
                                });
                            } else {
                                // 图片文件：不设置imgFileId和imgFileUrl
                                fileData = {
                                    fileId: file.fileId,
                                    fileUrl: file.fileUrl
                                    // 图片不设置 imgFileId 和 imgFileUrl
                                };
                                console.log('📁 处理检查项目图片文件:', {
                                    fileId: file.fileId,
                                    fileUrl: file.fileUrl
                                });
                            }

                            return fileData;
                        });
                    } else {
                        this.checkPhotos = [];
                    }
                }
            }
        },
        // 切换radio获取数据
        switchRadioGetDataFn(radioVal) {
            const _this = this;
            let array0 = [];
            const getArr0 = _this.$store.getters.getUploadImgs;
            // console.log('getArr0', getArr0);
            if (getArr0.length > 0) {
                getArr0.forEach((el) => {
                    if (
                        _this.checkData.checkId === el.checkId &&
                        _this.checkData.listId === el.listId &&
                        radioVal === el.checkStatus
                    ) {
                        // 获取图片地址
                        _this.checkPhotos = getArr0.checkPhotos;
                    }
                });
            } else {
                _this.checkPhotos = [];
            }
        },
        scanVideo(v, i) {
            console.log("预览视频url=", v);
            window.location.href = "https://mss.hfi-health.com:9443/" + v;
            return;
            const _this = this;
            _this.showVideo = true;
            _this.dp = new DPlayer({
                container: _this.$refs.video,
                video: {
                    url: v,
                    pic: i,
                    thumbnails: i,
                },
            });
        },
        closeVideoBox() {
            this.showVideo = false;
            this.dp.pause();
        },
    },
    components: {
        vRadio,
    },
    watch: {
        checkPhotos: {
            // 在vuex离储存提交数据
            handler: function (nv, ov) {
                // console.log('nv', nv);
                const _this = this;
                console.log(`🔥🔥🔥 checkPhotos Watch 被触发！`);
                console.log(`📸 检查项[${_this.checkData.listId}] "${_this.checkData.checkTip}" 照片变化:`, {
                    照片数量: nv ? nv.length : 0,
                    checkStatus: _this.val,
                    checkStatus含义: _this.val === null ? '未选择' : (_this.val === '1' ? '是' : '否'),
                    listId: _this.checkData.listId,
                    照片详情: nv
                });

                if (nv && nv.length >= 0) {
                    // hasVal:1=有照片；2:没有
                    const storeData = {
                        checkId: _this.checkId,
                        listId: _this.checkData.listId,
                        checkPhotos: nv.slice(),
                        checkStatus: _this.val,
                        hasVal: nv.length > 0 ? "1" : "2", // 根据实际照片数量设置
                    };
                    console.log(`  → 提交到Store:`, storeData);
                    console.log(`  → Store提交前验证:`, {
                        checkId存在: !!_this.checkId,
                        listId存在: !!_this.checkData.listId,
                        照片数组长度: storeData.checkPhotos.length,
                        hasVal: storeData.hasVal,
                        checkStatus: storeData.checkStatus,
                        checkStatus是否为null: storeData.checkStatus === null
                    });

                    // 如果有照片但没有选择是/否，给出提示
                    if (nv.length > 0 && _this.val === null) {
                        console.log(`⚠️⚠️⚠️ 检查项[${_this.checkData.listId}] 有照片但未选择是/否，数据不会被保存！`);
                        console.log(`💡 解决方案：请选择"是"或"否"按钮`);
                        // 可以选择在这里给出提示，但不阻止数据提交到Store
                        // _this.$toast.center('请先选择"是"或"否"');
                    }

                    console.log(`🚀 即将提交到 Vuex Store...`);
                    _this.$store.commit("setUploadImgs", storeData);
                    console.log(`✅ 已提交到 Vuex Store`);

                    // 验证 Store 中的数据
                    const storeData_verify = _this.$store.getters.getUploadImgs;
                    console.log(`🔍 Store验证 - 当前所有数据:`, storeData_verify);
                    const currentItem = storeData_verify.find(item =>
                        item.checkId === _this.checkId && item.listId === _this.checkData.listId
                    );
                    console.log(`🔍 Store验证 - 当前项目数据:`, currentItem);
                } else {
                    _this.checkPhotos = [];
                    const storeData = {
                        checkId: _this.checkId,
                        listId: _this.checkData.listId,
                        checkPhotos: [],
                        checkStatus: _this.val,
                        hasVal: "2",
                    };
                    console.log(`  → 提交到Store:`, storeData);
                    _this.$store.commit("setUploadImgs", storeData);
                }
            },
            immediate: false,
            deep: true,
        },
        // 草稿箱修改
        isModified: {
            handler: function (nv, ov) {
                if (nv) {
                    this.val = this.checkData.checkStatus;
                    if (this.checkData.checkPhotos !== "") {
                        const checkPhotos = JSON.parse(this.checkData.checkPhotos);
                        console.log('🔍 Watch监听草稿箱加载检查项目照片数据:', checkPhotos);

                        this.checkPhotos = checkPhotos.map((file) => {
                            // 区分图片和视频：通过文件扩展名判断
                            let fileData;
                            const fileUrl = file.fileUrl || '';
                            const isVideo = /\.(mp4|avi|mov|wmv|flv|webm|mkv)$/i.test(fileUrl);

                            if (file.imgFileId !== undefined || isVideo) {
                                // 视频文件：使用默认缩略图
                                fileData = {
                                    fileId: file.fileId,
                                    fileUrl: file.fileUrl,
                                    imgFileId: -1, // 视频使用默认缩略图标识
                                    imgFileUrl: defaultVideoThumb
                                };
                                console.log('📁 Watch处理检查项目视频文件:', {
                                    fileId: file.fileId,
                                    fileUrl: file.fileUrl,
                                    判断依据: file.imgFileId !== undefined ? 'imgFileId字段' : '文件扩展名'
                                });
                            } else {
                                // 图片文件：不设置imgFileId和imgFileUrl
                                fileData = {
                                    fileId: file.fileId,
                                    fileUrl: file.fileUrl
                                    // 图片不设置 imgFileId 和 imgFileUrl
                                };
                                console.log('📁 Watch处理检查项目图片文件:', {
                                    fileId: file.fileId,
                                    fileUrl: file.fileUrl
                                });
                            }

                            return fileData;
                        });
                    }
                }
            },
            immediate: true,
        },
        val: {
            handler: function (nv, ov) {
                // console.log('val===', nv);
                const _this = this;
                console.log(`🔄 检查项[${_this.checkData.listId}] "${_this.checkData.checkTip}" 状态变化:`, {
                    旧值: ov,
                    新值: nv,
                    checkId: _this.checkId,
                    listId: _this.checkData.listId,
                    picType: _this.checkData.picType,
                    当前照片数: _this.checkPhotos.length
                });
                if (nv === "0") {
                    // 判断否按钮是否可传照片
                    if (
                        _this.checkData.picType === 2 ||
                        _this.checkData.picType === 3
                    ) {
                        _this.showPicList = true;
                        _this.switchRadioGetDataFn(nv);
                        const storeData = {
                            checkId: _this.checkId,
                            listId: _this.checkData.listId,
                            checkPhotos: _this.checkPhotos
                                ? _this.checkPhotos.slice()
                                : [],
                            checkStatus: nv,
                            hasVal:
                                !!_this.checkPhotos &&
                                _this.checkPhotos.length > 0
                                    ? "1"
                                    : "2",
                        };
                        console.log(`  → 选"否"(可传照片), 提交Store:`, storeData);
                        _this.$store.commit("setUploadImgs", storeData);
                    } else {
                        _this.showPicList = false;
                        // 不能上传照片，但要更新初始checkStatus === null
                        const storeData = {
                            checkId: _this.checkId,
                            listId: _this.checkData.listId,
                            checkPhotos: [],
                            checkStatus: nv,
                            hasVal: "0",
                        };
                        console.log(`  → 选"否"(不传照片), 提交Store:`, storeData);
                        _this.$store.commit("setUploadImgs", storeData);
                    }
                } else if (nv === "1") {
                    // 判断否按钮是否可传照片
                    if (
                        _this.checkData.picType === 1 ||
                        _this.checkData.picType === 3
                    ) {
                        _this.showPicList = true;
                        _this.switchRadioGetDataFn(nv);
                        const storeData = {
                            checkId: _this.checkId,
                            listId: _this.checkData.listId,
                            checkPhotos: _this.checkPhotos
                                ? _this.checkPhotos.slice()
                                : [],
                            checkStatus: nv,
                            hasVal:
                                _this.checkPhotos &&
                                _this.checkPhotos.length > 0
                                    ? "1"
                                    : "2",
                        };
                        console.log(`  → 选"是"(可传照片), 提交Store:`, storeData);
                        _this.$store.commit("setUploadImgs", storeData);
                    } else {
                        _this.showPicList = false;
                        const storeData = {
                            checkId: _this.checkId,
                            listId: _this.checkData.listId,
                            checkPhotos: [],
                            checkStatus: nv,
                            hasVal: "0",
                        };
                        console.log(`  → 选"是"(不传照片), 提交Store:`, storeData);
                        _this.$store.commit("setUploadImgs", storeData);
                    }
                } else {
                    // checkStatus初次加载值为null
                    _this.showPicList = false;
                    const storeData = {
                        checkId: _this.checkId,
                        listId: _this.checkData.listId,
                        checkPhotos: [],
                        checkStatus: null,
                        hasVal: "2",
                    };
                    console.log(`  → 未选择(null), 提交Store:`, storeData);
                    _this.$store.commit("setUploadImgs", storeData);
                }
            },
            immediate: true,
        },
    },
};
</script>
<style lang="less" scoped>
@import "../../assets/css/mixin.less";
.check-item {
    .c1 {
        padding: 34px 30px 0 30px;
        font-size: 32px;
        color: #333;
        position: relative;
        &::after {
            .setBottomLine(#ECECEC);
        }
        ul {
            display: flex;
            padding: 28px 0 36px 0;
            li {
                flex: 0 0 188px;
            }
        }
    }
    .c2 {
        h2 {
            font-size: 28px;
            padding: 15px 30px;
            color: #317dff;
            background-color: #f0f7fd;
        }
        ul {
            padding: 36px 30px 42px 30px;
            display: flex;
            position: relative;
            li {
                position: relative;
                flex: 0 0 218px;
                padding-top: 218px;
                &:nth-child(1) {
                    margin-right: 18px;
                }
                &:nth-child(3) {
                    margin-left: 18px;
                }
                .i1 {
                    position: absolute;
                    left: 0;
                    top: 0;
                    width: 100%;
                    height: 100%;
                    border-radius: 6px;
                    z-index: 10;
                }
                .i1-play {
                    position: absolute;
                    left: 0;
                    top: 0;
                    width: 100%;
                    height: 100%;
                    z-index: 20;
                    background: url(../../assets/img/<EMAIL>) no-repeat
                        center center;
                    background-size: 81px 81px;
                }
                video {
                    position: absolute;
                    left: 0;
                    top: 0;
                    width: 208px;
                    height: 208px;
                }
                span {
                    display: block;
                    position: absolute;
                    right: 0;
                    top: 0;
                    width: 35px;
                    height: 35px;
                    background: url(../../assets/img/<EMAIL>) no-repeat
                        center center;
                    background-size: 35px 35px;
                    z-index: 30;
                }
                label {
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: url("../../assets/img/<EMAIL>") no-repeat
                        center center;
                    background-size: 100% 100%;
                    border-radius: 6px;
                    overflow: hidden;
                    input {
                        width: 1px;
                        height: 1px;
                        overflow: hidden;
                    }
                }
            }
            &::after {
                .setBottomLine(#ECECEC);
            }
        }
    }
    .select-two {
        position: fixed;
        bottom: 0;
        top: 0;
        left: 0;
        right: 0;
        z-index: 100;
        .st-mask {
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.2);
        }
        .st-content {
            position: absolute;
            bottom: 0;
            left: 0;
            width: calc(100% - 320px);
            padding: 0 160px;
            background: #fff;
            box-shadow: 0 2px 8px #317dff;
            overflow: hidden;
            .st-i {
                display: flex;
                .st-ic {
                    flex: 1;
                    text-align: center;
                    padding: 37px 0;
                    position: relative;
                    label {
                        display: block;
                        width: 116px;
                        height: 116px;
                        background: url("../../assets/img/<EMAIL>")
                            no-repeat center center;
                        background-size: 116px auto;
                        overflow: hidden;
                        input {
                            width: 1px;
                            height: 1px;
                            overflow: hidden;
                        }
                    }
                    p {
                        font-size: 32px;
                        color: #000;
                        font-weight: bold;
                        width: 116px;
                        text-align: center;
                        padding: 16px 0;
                    }
                    img {
                        display: block;
                        width: 116px;
                    }
                }
                .st-icrt {
                    text-align: right;
                    img {
                        display: inline-block;
                    }
                    p {
                        float: right;
                    }
                }
            }
        }
    }
    .vi {
        position: fixed;
        bottom: 0;
        top: 0;
        left: 0;
        right: 0;
        z-index: 100;
        .vi-mask {
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
        }
        .vi-content {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 100%;
            .vi-main {
                padding: 0 30px;
                position: absolute;
                top: 50%;
                left: 0;
                transform: translateY(-50%);
                width: calc(100% - 60px);
                z-index: 30;
                /deep/ .dplayer {
                    .dplayer-controller {
                        height: 41px; /*no*/
                        padding: 0 20px; /*no*/
                        .dplayer-icons.dplayer-comment-box {
                            height: 38px; /*no*/
                            left: 20px; /*no*/
                            right: 20px; /*no*/
                            .dplayer-icon {
                                padding: 7px; /*no*/
                                width: 40px; /*no*/
                            }
                        }
                        .dplayer-icons.dplayer-icons-left {
                            height: 38px; /*no*/
                            .dplayer-icon.dplayer-play-icon {
                                padding: 7px; /*no*/
                                width: 40px; /*no*/
                                margin: 0 -3px; /*no*/
                            }
                            .dplayer-volume {
                                .dplayer-icon.dplayer-volume-icon {
                                    width: 43px; /*no*/
                                    padding: 7px; /*no*/
                                    margin: 0 -3px; /*no*/
                                }
                                .dplayer-volume-bar-wrap {
                                    margin: 0 10px 0 -5px; /*no*/
                                    .dplayer-volume-bar {
                                        top: 17px; /*no*/
                                        width: 0; /*no*/
                                        height: 3px; /*no*/
                                        .dplayer-volume-bar-inner {
                                            .dplayer-thumb {
                                                right: 5px; /*no*/
                                                margin-top: -4px; /*no*/
                                                margin-right: -10px; /*no*/
                                                height: 11px; /*no*/
                                                width: 11px; /*no*/
                                            }
                                        }
                                    }
                                }
                                [data-balloon-pos="up"]:after {
                                    margin-bottom: 11px; /*no*/
                                }
                                [data-balloon][data-balloon-pos="up"]:after,
                                [data-balloon][data-balloon-pos="up"]:before {
                                    -webkit-transform: translate(
                                        -50%,
                                        10px
                                    ); /*no*/
                                    transform: translate(-50%, 10px); /*no*/
                                }
                            }
                            .dplayer-time {
                                line-height: 38px; /*no*/
                                font-size: 13px; /*no*/
                            }
                        }
                        .dplayer-icons.dplayer-icons-right {
                            right: 20px; /*no*/
                            height: 38px; /*no*/
                            .dplayer-setting {
                                .dplayer-icon.dplayer-setting-icon {
                                    padding: 8px; /*no*/
                                    padding-top: 8.5px; /*no*/
                                    width: 40px; /*no*/
                                }
                                .dplayer-setting-box {
                                    bottom: 50px; /*no*/
                                    width: 150px; /*no*/
                                    border-radius: 2px; /*no*/
                                    padding: 7px 0; /*no*/
                                }
                            }
                            .dplayer-full {
                                .dplayer-icon.dplayer-full-in-icon {
                                    top: -30px; /*no*/
                                    padding: 8px; /*no*/
                                    width: 40px; /*no*/
                                }
                                .dplayer-icon.dplayer-full-icon {
                                    padding: 8px; /*no*/
                                    width: 40px; /*no*/
                                }
                            }
                        }
                        .dplayer-bar-wrap {
                            padding: 5px 0; /*no*/
                            bottom: 33px; /*no*/
                            width: calc(100% - 40px); /*no*/
                            height: 3px; /*no*/
                        }
                    }
                    .dplayer-controller-mask {
                        height: 98px; /*no*/
                    }
                }
            }
            .vic-bg {
                position: absolute;
                bottom: 0;
                left: 0;
                width: 100%;
                height: 100%;
                z-index: 29;
            }
        }
    }
}
</style>
