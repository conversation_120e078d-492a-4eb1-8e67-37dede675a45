import { getUserInfoFromUrl, mergeUser } from '@/assets/js/until.js'
import { getJtToken } from '@/api/check'
import { getUserIn } from '@/api/sign'

/**
 * 简化的用户信息管理服务
 * 只使用URL参数中的authToken和origin来获取用户信息
 */
class UserService {
    constructor() {
        this.userInfo = {};
        this.isInitialized = false;
    }

    /**
     * 初始化用户信息
     * 从URL参数获取authToken和origin，然后调用API获取用户信息
     */
    async initUserInfo() {
        try {
            // 1) 优先：从URL参数读取，若存在则刷新并覆盖本地
            const urlParams = getUserInfoFromUrl();
            const { authToken, origin } = urlParams;

            console.log('🔍 URL参数解析结果:', urlParams);
            console.log('🔍 提取的认证参数:', { authToken, origin });

            if (authToken && origin) {
                console.log('✅ 检测到URL携带认证参数，开始刷新用户信息');
                console.log('🚀 调用getJtToken API，参数:', { authToken, channelId: origin });

                const apiResponse = await getJtToken({
                    authToken: authToken,
                    channelId: origin
                });
                console.log('📥 getJtToken API响应:', apiResponse);

                if (apiResponse && apiResponse.errorCode === '0') {
                    // 1) 由网关返回合并出基础用户
                    const baseUser = mergeUser(apiResponse);

                    // 2) 使用手机号从后台获取“当前用户信息”，进行二次合并
                    let finalUser = baseUser;
                    try {
                        if (baseUser && baseUser.phone) {
                            const currentUserRes = await getUserIn({ phone: baseUser.phone });
                            if (currentUserRes && (currentUserRes.errorCode === '0' || currentUserRes.errorCode === 0)) {
                                // 将后台当前用户信息覆盖/补全到基础用户信息
                                finalUser = { ...baseUser, ...(currentUserRes.data || {}) };
                            }
                        }
                    } catch (e) {
                        console.warn('通过手机号获取后台当前用户信息失败，将继续使用基础用户信息:', e);
                    }

                    // 3) 保存并就绪
                    this.saveUserInfoToStorage(finalUser, apiResponse);
                    this.userInfo = finalUser;
                    this.isInitialized = true;
                    return {
                        success: true,
                        source: 'api',
                        userInfo: finalUser,
                        rawResponse: apiResponse
                    };
                } else {
                    console.error('❌ getJtToken API返回错误:', apiResponse);
                    throw new Error(`API返回错误: ${apiResponse?.errorCode} - ${apiResponse?.value || apiResponse?.message}`);
                }
            } else {
                console.log('⚠️ URL中缺少认证参数:', { authToken: !!authToken, origin: !!origin });
            }

            // 2) 其次：无URL参数时，使用本地存储
            console.log('📦 尝试从本地存储获取用户信息...');
            const cachedUser = this.getUserInfoFromStorage();
            console.log('📦 本地存储的用户信息:', cachedUser);
            if (cachedUser && cachedUser.phone) {
                console.log('✅ 使用本地存储的用户信息');
                this.userInfo = cachedUser;
                this.isInitialized = true;
                return {
                    success: true,
                    source: 'storage',
                    userInfo: cachedUser,
                    rawResponse: null
                };
            }

            // 3) 无URL且无缓存，返回未初始化
            console.log('❌ 无URL参数且无本地缓存，初始化失败');
            return {
                success: false,
                error: '缺少必要参数且无本地缓存'
            };

        } catch (error) {
            console.error('初始化用户信息失败:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 保存用户信息到localStorage
     */
    saveUserInfoToStorage(userInfo, apiResponse) {
        try {
            // 保存用户基本信息
            window.localStorage.setItem("userInfo", JSON.stringify(userInfo));
            console.log('用户信息已保存到localStorage');
        } catch (error) {
            console.error('保存用户信息到localStorage失败:', error);
        }
    }

    /**
     * 替代原有的initCookie功能
     * 使用新的认证方式，不再需要authCode
     */
    async initCookieWithNewAuth() {
        // 新的认证方式已经在initUserInfo中完成
        // 这里只是为了兼容性保留接口
        if (this.isInitialized) {
            return {
                success: true,
                message: '用户已通过新方式认证'
            };
        } else {
            return {
                success: false,
                message: '用户未初始化，请先调用initUserInfo'
            };
        }
    }

    // getUserInfoCompat 已废弃，直接使用 getUserInfo()

    /**
     * 获取用户位置信息
     * 从URL参数获取，如果没有则返回默认位置
     */
    async getUserLocation() {
        try {
            const urlParams = getUserInfoFromUrl();

            // 如果URL中有位置信息，直接使用
            if (urlParams.longitude && urlParams.latitude) {
                const location = {
                    longitude: parseFloat(urlParams.longitude),
                    latitude: parseFloat(urlParams.latitude)
                };
                // 保存到本地存储
                this.saveUserLocationToStorage(location);
                return location;
            }

            // 优先从本地存储读取历史位置
            const cachedLocation = this.getUserLocationFromStorage();
            if (
                cachedLocation &&
                typeof cachedLocation.longitude === 'number' &&
                typeof cachedLocation.latitude === 'number'
            ) {
                return cachedLocation;
            }

            this.saveUserLocationToStorage({
                longitude: 120.21,
                latitude: 30.26
            });
            // 如果没有位置信息，返回默认位置（杭州）
                return {
                longitude: 120.21,
                latitude: 30.26
            };

        } catch (error) {
            console.error('获取用户位置失败:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 保存用户位置信息到localStorage
     */
    saveUserLocationToStorage(location) {
        try {
            if (
                location &&
                typeof location.longitude === 'number' &&
                typeof location.latitude === 'number'
            ) {
                window.localStorage.setItem('userLocation', JSON.stringify(location));
                console.log('位置信息已保存到localStorage');
            }
        } catch (error) {
            console.error('保存位置信息到localStorage失败:', error);
        }
    }

    /**
     * 从localStorage获取用户位置信息
     */
    getUserLocationFromStorage() {
        try {
            const locationStr = window.localStorage.getItem('userLocation');
            const parsed = locationStr ? JSON.parse(locationStr) : null;
            if (
                parsed &&
                typeof parsed.longitude === 'number' &&
                typeof parsed.latitude === 'number'
            ) {
                return parsed;
            }
            return null;
        } catch (error) {
            console.error('从localStorage获取位置信息失败:', error);
            return null;
        }
    }

    /**
     * 获取当前用户信息
     */
    getUserInfo() {
        return this.userInfo;
    }

    /**
     * 检查是否已初始化
     */
    isUserInitialized() {
        return this.isInitialized;
    }

    /**
     * 从localStorage获取已保存的用户信息
     */
    getUserInfoFromStorage() {
        try {
            const userInfoStr = window.localStorage.getItem("userInfo");
            return userInfoStr ? JSON.parse(userInfoStr) : {};
        } catch (error) {
            console.error('从localStorage获取用户信息失败:', error);
            return {};
        }
    }
}

// 创建单例实例
const userService = new UserService();

export default userService;
