<template>
<div class="s">
	<div class="s-map" ref="container" id="container"></div>
	<div class="relLocation" @touchstart="relLocation"><img src="../../assets/img/relLocation.png" alt=""> <span>重新定位</span></div>
    <div class="searchbar">
		<div class="searchbar-inner">
			<div class="searchbar-inner-location" v-show="currentCity">{{currentCity}}</div>
			<div class="search-input-con">
				<i class="search-input-icon"></i>
				<input type="search" ref="search"  placeholder="输入要搜索的机构ID/名称/地址" class="search-input" @focus="searchOrg"/>
				<!-- <i class="search-input-icon-right" v-show="showRightIcon" @touchstart="clear"></i> -->
			</div>
		</div>
    </div>
	<div class="model" v-show="modelShow">
		<div class="sign-title">签到地址</div>
		<div v-if="list.length > 0">
			<ul class="sigh-ul" :class="checkedNames ? 'sigh-ul-height2':''">
				<li class="sign-list" v-for="(item,index) in list" :key="item.id" >
					<div class="sign-name">
						<h3 class="medical-name">
							<span :class="item.busiStatus || item.orgLevel ? 'orgName':'orgName noright'">{{item.orgName}}</span>
							<span class="busiStatus" v-if="item.busiStatus">{{filterOptions('busiStatus',item.busiStatus)}}</span>
							<span class="orgLevel" v-if="item.orgLevel">{{filterOptions('orgLevel',item.orgLevel)}}</span>
						</h3>
						<p class="sign-loacation">地址:{{item.orgAddress}}</p>
					</div>
					<div class="sign-box" :class="item.orgId === checkedNames ? 'checked': ''" @click="location(item.orgLatitude, item.orgLongitude, list,item,index)">
						<input type="radio" :value="item.orgId"  @click="change(item)" >
					</div>
				</li>
			</ul>
			<div>
				<div class="sign-tip" @touchstart="showTips" v-if="checkedNames">
					<img src="@/assets/img/error.png"/>信息有误，报告该地点问题</div>
				<!-- <div class="btn-plain-circle  sign-add-btn" @touchstart="addOrg" v-if="!checkedNames">补充坐标机构</div> -->
				<div class="sign-btn" :class="checkedNames === '' ? 'btnDisable': ''" @touchstart="confirmCheckIn">确认签到</div>
			</div>
		</div>
		<div v-else class="nodata">
			<img src="@/assets/img/sign-no-data.png"/>
			<div>
				附近暂无相关机构
		    </div>
		</div>
    </div>
	
	<div class="sign-modal" v-show="modalShow" @touchstart="closeModal">
		<div class="custom-modal-body">
			<div class="custom-modal-content">
				<div class="custom-modal-content-row" @touchstart="modifyInfo">
					<div>
						<p class="custom-modal-content-title">修改信息</p>
						<p class="custom-modal-content-subtitle">修改机构实际所在地信息</p>
					</div>
					 <div class="enter-icon">
					 </div>
				</div>
				<div class="custom-modal-content-row" @touchstart="findNoPalce"> 
					<div>
						<p class="custom-modal-content-title">地点关闭/找不到</p>
						<p class="custom-modal-content-subtitle">该地点已经关闭/未找到该地点</p>
					</div>
					<div class="enter-icon">
					 </div>
				</div>
			</div>
		</div>
		<div class="close-icon">
		</div>
	</div>
	 <!-- <modal :show="modalShow" @close="closeModal" class="sign-modal">
		
		<div class="custom-modal-content-row" @touchstart="modifyInfo">
			<div>
				<p class="custom-modal-content-title">修改信息</p>
				<p class="custom-modal-content-subtitle">修改机构实际所在地信息</p>
			</div>
				<div class="enter-icon">
				</div>
		</div>
		<div class="custom-modal-content-row" @touchstart="findNoPalce"> 
			<div>
				<p class="custom-modal-content-title">地点关闭/找不到</p>
				<p class="custom-modal-content-subtitle">该地点已经关闭/未找到该地点</p>
			</div>
			<div class="enter-icon">
				</div>
		
		 </div>
	</modal> -->
	<!-- 找不到 -->
	<modal :show="modalNoPosShow" :showClose="false" @close="closeModalNoPos">
		<div>
			<div class="success-icon">
			</div>
			<div class="custom-modal-content-title">
				<p>感谢您提供修改意见，</p>
				<p>我们会尽快核准并更正。</p>   
			</div>
		</div>
    </modal>
</div>
</template>
<script>

import currentImg from '@/assets/img/<EMAIL>'
import locationImg from '@/assets/img/<EMAIL>'
import ydIcon from '@/assets/img/ydIcon.png'
import yyIcon from '@/assets/img/yyIcon.png'
import { queryOrganByMyCoordinate, checkIn, addRecoveryOrgan } from '@/api/sign'
import {init} from '@/until/until'
import {getOptions} from '@/assets/js/until.js'
import modal from '@/components/customModal/modal.vue'
import { getStoredUserLocation } from '@/assets/js/until.js'
export default {
	name:'sign',
	components:{
        modal
    },
	data () {
		return {
			url:'http://**********/#/index',
			map: null,
            current: null,
            list: [],
			checkedNames: '',
			locationval: '',
			checked: false,
			checkLongitude: '',
			checkLatitude: '',
			orgId: '',
			selected: '',
			lats:'',
			selectedIndex: 0,
			markerArr: [],
			modelShow: false,
			modalShow:false,
			modalNoPosShow:false,
			selectedItem:{},
			busiStatusOptions:getOptions('busiStatus'),
			orgLevelOptions:getOptions('orgLevel'),
			showRightIcon: false,
			currentCity: ''
		}
	},
	mounted () {
		this.$loading(true, '');
		this.currentCity = sessionStorage.getItem('currentCity')
		this.getLocation();

		//ceshi
		// this.initMap(30.258481, 120.207290);
		// this.queryOrganByMyCoordinate(30.258481, 120.207290)
	},
	watch: {
		// searchValue(nv, ov){
		// 	const _this = this;
		// 	if (nv === '') {
		// 		this.showRightIcon = false
		// 	} else {
		// 		this.showRightIcon = true
		// 	}
		// }
	},
	methods: {
		searchOrg(){
			this.$router.push(
				{
					path:'/searchOrg'
				}
			)
		},
		filterOptions (type,value) {
			let currentData = []
           if(type === 'busiStatus'){
			  currentData = this.busiStatusOptions.filter(item=>{
				   return item.key == value
			   })
		   }
		   if(type === 'orgLevel'){
			   currentData = this.orgLevelOptions.filter(item=>{
				   return item.key == value
			   })
		   }
		   return currentData.length > 0 ? currentData[0].value : ''
		},
		// 获取当前坐标 - 已替换为新的位置服务
		async getLocation () {
			try {
				console.log('开始获取位置信息...');
				const locationResult = getStoredUserLocation();
				console.log('位置信息获取结果:', locationResult);

				if (locationResult.success) {
					const { longitude, latitude } = locationResult.location;
					console.log('使用位置:', longitude, latitude, '来源:', locationResult.source);
					this.checkLongitude = longitude;
					this.checkLatitude = latitude;
					if(!this.currentCity){
						this.getRegeCode(this.checkLongitude, this.checkLatitude, {}, false);
					}
					this.initMap(latitude, longitude);
					this.queryOrganByMyCoordinate(latitude, longitude);
				} else {
					console.log('位置获取失败，给出默认位置');
					this.checkLongitude = 120.14738921730009;
					this.checkLatitude = 30.244121753904395;
					this.getRegeCode(this.checkLongitude, this.checkLatitude, {}, false);
					this.initMap(this.checkLatitude, this.checkLongitude);
					this.queryOrganByMyCoordinate(this.checkLatitude, this.checkLongitude);
				}
			} catch (error) {
				console.error('获取位置信息异常:', error);
				// 给出默认位置
				this.checkLongitude = 120.14738921730009;
				this.checkLatitude = 30.244121753904395;
				this.getRegeCode(this.checkLongitude, this.checkLatitude, {}, false);
				this.initMap(this.checkLatitude, this.checkLongitude);
				this.queryOrganByMyCoordinate(this.checkLatitude, this.checkLongitude);
			}
		},

		relLocation () {
			this.checkedNames = '';
			const fallback = {
				longitude: 120.14738921730009,
				latitude: 30.244121753904395
			};
			try {
				const locationResult = getStoredUserLocation() || {};
				const hasLocation = locationResult.success &&
					locationResult.location &&
					typeof locationResult.location.longitude === 'number' &&
					typeof locationResult.location.latitude === 'number';
				const { longitude, latitude } = hasLocation
					? locationResult.location
					: fallback;
				this.checkLongitude = longitude;
				this.checkLatitude = latitude;
				if (!this.currentCity) {
					this.getRegeCode(this.checkLongitude, this.checkLatitude, {}, false);
				}
				this.initMap(latitude, longitude);
				this.queryOrganByMyCoordinate(latitude, longitude);
			} catch (error) {
				console.error('重新定位失败:', error);
				this.checkLongitude = fallback.longitude;
				this.checkLatitude = fallback.latitude;
				this.initMap(fallback.latitude, fallback.longitude);
				this.queryOrganByMyCoordinate(fallback.latitude, fallback.longitude);
				this.$toast.center('未获取到您的定位信息，已使用默认位置');
			}
		},
		// 初始化地图
		initMap (lat, lng) {
			console.log('初始化map');
			const _this = this; 
			const AMap = window.AMap;
			_this.map = new AMap.Map(_this.$refs.container, {
				resizeEnable: true,
				center: new AMap.LngLat(lng, lat),
				zoom: 16
			});
			
			// 当前位置
			_this.current = new AMap.Marker({
				icon: new AMap.Icon({
					image: currentImg,
					size: new AMap.Size(24, 24), //图标大小
					imageSize: new AMap.Size(24, 24)
				}),
				map: _this.map,
				position: _this.map.getCenter(),
				zIndex: 200
			});
			let circle = new AMap.Circle({
				center: new AMap.LngLat(lng, lat),
				radius: 500, //半径
				borderWeight: 3,
				strokeColor: "#317DFF", 
				strokeOpacity: 0.15,
				// strokeWeight: 6,
				strokeOpacity: 0,
				fillOpacity: 0.15,
				strokeStyle: '',
				// strokeDasharray: [10, 10], 
				// 线样式还支持 'dashed'
				fillColor: '#317DFF',
				zIndex: 50,
			});
			circle.setMap(_this.map);
		},
		change (obj) {
			if(this.checkedNames != obj.orgId){
				this.checkedNames = obj.orgId
			    this.selectedItem = Object.assign({},obj)
			}else{
				this.checkedNames = ''
			    this.selectedItem = {}
			}
		},
		initCookie () {
			let that = this
			window.yl.call('getAuthcode', {}, {
				onSuccess: (res) => {
					console.log(res.param.authCode)
					initCookie({
						authCode: res.param.authCode
					}).then((res)=>{
						console.log(1, res)
						if(res.errorCode === '0') {
							that.queryOrganByMyCoordinate()
						} else if(res.errorCode === '1003'){
							that.$toast.center(res.val)
						} else {
							that.$toast.center(res.val)
						}
					}).catch((e)=>{
						console.log(e)
					})
				},
				onFail: (res) => {

				}
			})
		},
		// 获取签到附近地点列表
		queryOrganByMyCoordinate (lat, lng) {
			console.log('queryOrganByMyCoordinate===进来了',lat, lng)
			queryOrganByMyCoordinate({
				currentLongitude: lng,
				currentLatitude: lat
			}).then((res)=>{
				if(res.errorCode === '0') {
					this.list = res.data
					this.drawMarker(this.list)
					this.$loading(false, '');
					setTimeout(()=>{
						this.modelShow = true
					},500)
				} else if (res.errorCode == '1003') {
					this.initCookie()
				}
			}).catch((e)=>{
				this.$toast.center(e)
			})
		},
		confirmCheckIn () {
			// 签到
			console.log('签到', this.checkedNames,this.checkLongitude, this.checkLatitude )
			if(this.checkedNames) {
				this.selected = true
				checkIn({
					orgId: this.checkedNames,
					checkLongitude: this.checkLongitude,
					checkLatitude: this.checkLatitude
				}).then((res)=>{
					console.log(10, res);
					if(res.errorCode === '0') {
						this.$router.push({
							path: 'identityCheck',
							query: {
								checkId: res.data
							}
						})
					} else if(res.errorCode === '10003'){
						this.initCookie()
					}
				}).catch((e) => {

				})
			} else {
				this.selected = false
				this.$toast.center('请勾选签到地址')
			}
		},
		location(lat, lng, list,item,index) {
			const AMap = window.AMap;
			const _this = this;
			const currentInfo = item ? item : {}
			_this.map.remove(_this.lats);
			console.log(this.checkedNames)
			if(this.checkedNames) {
				list.forEach((v,i)=>{
					let currentInfo = list[i]
					_this.markerArr[i].setIcon(new AMap.Icon({
							image: currentInfo.orgType == 1 ? ydIcon : yyIcon,
							size: new AMap.Size(30, 35),  //图标大小
							imageSize: new AMap.Size(30, 35)
					}));
					_this.markerArr[i].setOffset(new AMap.Pixel(-15,-35));
					if(currentInfo.orgId == item.orgId){
						_this.markerArr[i].setIcon(new AMap.Icon({
							image: currentInfo.orgType == 1 ? ydIcon : yyIcon,
							size: new AMap.Size(51, 59.5),  //图标大小
							imageSize: new AMap.Size(51, 59.5),
						}));
						_this.markerArr[i].setOffset(new AMap.Pixel(-25.5,-59.5));
						_this.selectedItem = Object.assign({},item)
						_this.checkedNames = item.orgId
					}
				})
			}else{
				_this.markerArr[index].setIcon(new AMap.Icon({
					image: currentInfo.orgType == 1 ? ydIcon : yyIcon,
					size: new AMap.Size(30, 35),  //图标大小
					imageSize: new AMap.Size(30, 35)
				}));
				_this.markerArr[index].setOffset(new AMap.Pixel(-15,-35));
			}
		},
		removePoint(i) {
			_this.map.remove(i);
		},
		drawMarker (points,lat,lng) {
			const _this = this;
			console.log('drawMarker', points);
			let pointArr = [];
			const AMap = window.AMap;
			_this.map.remove(_this.markerArr);
			// 路线
			points.forEach((el, index) => {
				pointArr[index] = new AMap.LngLat(el.orgLongitude, el.orgLatitude);
					_this.markerArr[index] = new AMap.Marker({
						icon: new AMap.Icon({
							image: el.orgType == 1 ? ydIcon : yyIcon,
							size: new AMap.Size(30, 35),  //图标大小
							imageSize: new AMap.Size(30, 35)
						}),
						offset: new AMap.Pixel(-15,-35),
						map: _this.map,
						position: pointArr[index],
						zIndex: 200
					});
				// marker点击事件
        		AMap.event.addListener(_this.markerArr[index], 'click', function (e) {
					console.log(e)
					console.log(points[index])
					// 将除当前点的图标变为正常大小
					pointArr.forEach((v,i)=>{
						let currentInfo = points[i]
						_this.markerArr[i].setIcon(new AMap.Icon({
							image: currentInfo.orgType == 1 ? ydIcon : yyIcon,
							size: new AMap.Size(30, 35),  //图标大小
							imageSize: new AMap.Size(30, 35)
						}));
						_this.markerArr[i].setOffset(new AMap.Pixel(-15,-35));
					})
					// 当前定位图标变大
					// if(_this.checkedNames){
						_this.markerArr[index].setIcon(new AMap.Icon({
							image: el.orgType == 1 ? ydIcon : yyIcon,
							size: new AMap.Size(51, 59.5),  //图标大小
							imageSize: new AMap.Size(51, 59.5)
						}));
						_this.selectedItem = Object.assign({},points[index])
						_this.checkedNames = points[index].orgId
						_this.markerArr[index].setOffset(new AMap.Pixel(-25.5,-59.5));
					// }
					// _this.location(pointArr[index].lat,pointArr[index].lngt)
					console.log(_this.checkedNames)
				});

			})
		},
		// 补充机构
		addOrg(){
            this.$router.push({
			   path:'/searchLocation'
		    })
		},
		// 弹窗选择【修改地址】/【地点关闭/找不到]
		showTips(){
			this.modalShow = true
		},
		closeModal(){
			this.modalShow = false
		},
		closeModalNoPos(){
            this.modalNoPosShow = false
		},
		// 修改信息
		modifyInfo(){
		   this.$store.commit('updateLocationItem', this.selectedItem);
		   this.$store.commit('updateShowTipStatus',false)
           this.$router.push({
			   path:'/locationCorrect/1'
		   })
		},
		// 关闭
		findNoPalce(){
			var that = this
			that.modalShow = false
			that.$loading(true,'')
			var item = Object.assign({},that.selectedItem)
			item.submitType = 2
			this.getRegeCode(item.orgLongitude,item.orgLatitude,item,true)
		},
		getRegeCode(lnt,lat,item,flag){
			var that = this
			AMap.plugin('AMap.Geocoder',function(){//异步加载插件
				var geocoder = new AMap.Geocoder({
					city: "全国",
					radius: 1000 
				});
				var lnglat = [lnt,lat]
				geocoder.getAddress(lnglat, function(status, result) {
					console.log(result)
					if(flag){
						if (status === 'complete'&&result.regeocode) {
							that.$set(item,'orgAreasBelongs',result.regeocode.addressComponent.district)
							that.$set(item,'orgBelongsStreet',result.regeocode.addressComponent.township)	
						}
						addRecoveryOrgan(item).then(res=>{
							if(res.errorCode == 0){
								that.modalNoPosShow = true
								setTimeout(function(){
									that.modalNoPosShow = false
								},2000)
							}else{
								that.$toast.center(res.value)
							}
								that.$loading(false,'')
							}).catch(error=>{
								that.$loading(false,'')
						})
					}else{
						if (status === 'complete'&&result.regeocode) {
							that.currentCity = result.regeocode.addressComponent.city
							that.currentCityCode = result.regeocode.addressComponent.cityCode
							var index = that.currentCity.indexOf('市')
							if(index != -1 ){
								that.currentCity = that.currentCity.slice(0,index)
							}
							sessionStorage.setItem(that.currentCity,'currentCity')
						}
					}
				});
			});
		},
	},
	watch: {}
};
</script>
<style lang="less" scoped>
@import '../../assets/css/mixin.less';
.s{
	width: 100%;
	position: relative;
	line-height: 1.5;
	overflow: auto;
	height: 100%;
	-webkit-overflow-scrolling: touch;
	.s-map{
		width: 100%;
		height: 100%;
	}
	.sign-modal {
		position: absolute;
		top: 0;
		left: 0;
		height: 100%;
		width: 100%;
		background-color: rgba(0, 0, 0, 0.3);
		&-spLine {
			width: 100%;
			height: 1px;
			background-color: #f0e7e7;
		}
		.custom-modal-body {
			position: absolute;
			top: 15%;
			left: 50px;
			right: 50px;
			border-radius: 8px;
			background-image: url(../../assets/img/errorBg.png);
			background-repeat: no-repeat;
			background-size: 100%;
			height: 700px;
			background-color: transparent;
		}
		.custom-modal-content {
			position: absolute;
			left: 30px;
			right: 30px;
			bottom: 0px;
			margin: 0 auto;
			&-row {
				background: #F0F5FD;
				padding: 30px;
				border-radius: 8px;
				margin-bottom: 30px;
				display: flex;
				align-items: center;
				div:first-child{
					flex: 1;
				}
			}
			&-title {
				font-size: 34px;
				margin-bottom: 5px;
				color: #333333;
				text-align: left;
				margin: auto;
			}
			&-subtitle {
				font-size: 32px;
				color: #777777;
			}
		}
		.close-icon {
			background-image: url(../../assets/img/close.png);
			background-repeat: no-repeat;
			background-size: 72px 72px;
			height: 72px;
			width: 72px;
			position: absolute;
			bottom: 60px;
			left: 50%;
			margin-left: -36px;
		}
	}
	// .custom-modal{
	// 	position: absolute;
	// 	top: 0;
	// 	left: 0;
	// 	height: 100%;
	// 	width: 100%;
	// 	background-color: rgba(0, 0, 0, 0.3);
	// 	&-spLine {
	// 		width: 100%;
	// 		height: 1px;
	// 		background-color: #f0e7e7;
	// 	}
	// 	/deep/.custom-modal-body {
	// 		position: absolute;
	// 		top: 15%;
	// 		left: 50px;
	// 		right: 50px;
	// 		border-radius: 8px;
	// 		background-image: url(../../assets/img/errorBg.png);
	// 		background-repeat: no-repeat;
	// 		background-size: 100%;
	// 		height: 700px;
	// 		background-color: transparent;
	// 	}
	// 	/deep/.custom-modal-content {
	// 		position: absolute;
	// 		left: 30px;
	// 		right: 30px;
	// 		bottom: 0px;
	// 		margin: 0 auto;
	// 		&-row {
	// 			background: #F0F5FD;
	// 			padding: 30px;
	// 			border-radius: 8px;
	// 			margin-bottom: 30px;
	// 			display: flex;
	// 			align-items: center;
	// 			div:first-child{
	// 				flex: 1;
	// 			}
	// 		}
	// 		&-title {
	// 			font-size: 34px;
	// 			margin-bottom: 5px;
	// 			color: #333333;
	// 			text-align: left;
	// 			margin: auto;
	// 		}
	// 		&-subtitle {
	// 			font-size: 32px;
	// 			color: #777777;
	// 		}
	// 	}
	// 	.close-icon {
	// 		background-image: url(../../assets/img/close.png);
	// 		background-repeat: no-repeat;
	// 		background-size: 72px 72px;
	// 		height: 72px;
	// 		width: 72px;
	// 		position: absolute;
	// 		bottom: 60px;
	// 		left: 50%;
	// 		margin-left: -36px;
	// 	}
	// }
}
.relLocation{
	position:fixed;
	bottom:662px;
	left:27px;
	display:flex;
	justify-content: center;
	align-items: center;
	background:#fff;
	box-shadow:0px 2px 8px 1px rgba(179,180,189,0.25);
	width:204px;
	height:56px;
	// border:1px solid #ddd;
	border-radius:30px;
	color:#333333;
	font-size:30px;
	img{
		width:32px;
		height:32px;
		margin-right:10px;
	}
}
.model{
    position:fixed;
    bottom:0;
    width:100%;
    height:638px;
	background:#fff;
	.sigh-ul{
		height:392px;
		&-height2 {
         height:320px;
		}
		overflow-y: auto;
		.sign-list{
			display:flex;
			justify-content: space-between;
			align-items: center;
			padding:18px 28px;
			border-bottom:1px solid #ececec;
			.sign-name{
				margin-right: 58px;
				.orgName {
					margin-right: 10px;
				}
				.noright {
					margin-right: 0;
				}
				.orgLevel {
					color: #2CD897;
					border: 2px solid #2CD897;
					display: inline-block;
					font-size: 26px;
					padding: 0px 16px;
					font-weight: 500;
					border-radius: 6px;
					box-sizing: border-box;
					// height: 34px;
					// line-height: 34px;
				}
				.busiStatus {
					color: #333333;
					border: 2px solid #333333;
					display: inline-block;
					font-size: 26px;
					padding: 0px 16px;
					font-weight: 500;
					border-radius: 6px;
					box-sizing: border-box;
					margin-right: 10px;
					// height: 34px;
					// line-height: 34px;
				}
			}
			.medical-name{
				font-size:32px;
				color:#333;
				margin-bottom: 8px;
			}
			.sign-loacation{
				font-size:28px;
				color:#777;
			}
			.sign-box{
				width:40px;
				height:40px;
				position: relative;
				// overflow: hidden;
				input{
					background:#fff;
					border:none;
					// display: none;
				}
				input[type=radio]{
					width:35px;
					height:35px; 
					color:#fff;
					outline: none;
					position: absolute;
					top: 0;
					bottom: 0;
					left: 0;
					right: 0;
					margin: auto;
				}
				input[type=radio]:after 
				{
					width: 40px;
					height: 40px;
					position: absolute;
					left: 50%;
					top: 50%;
					transform: translate3d(-50%, -50%,0);
					content: "";
					background-color:White;
					color: #000000;
					display: inline-block;
					border-radius: 2px;
					border:1px solid #ececec;/*no*/
				}
			}
			.checked{
				input:after{
					content: "";
					width: 40px;
					height: 40px;
					position: absolute;
					left: -3px;
					top: -3px;
					background:url('../../assets/img/checkbox.png') no-repeat center;
					background-size:100%;
					border:none !important;
				}
			}
		}
		.sign-list:first-child{
			// border-top:1px solid #ececec;
		}
		
	}
	.sign-title{
		height:110px;
		line-height:110px;
		font-size:34px;
		color:#000;
		padding-left:28px;
		font-weight:bold;
		border-bottom: 2px solid #ececec;
	}
	.sign-btn{
		width:690px;
		height:98px;
		line-height: 98px;
		color:#fff;
		font-size:32px;
		text-align:center;
		background:#317dff;
		margin:0 auto 0 auto;
		border-radius:10px;
	}
	.sign-tip {
	   font-size: 28px;
	   color: #F65659;
	   padding: 15px 30px 10px;
	   margin-bottom: 20px;
	   img {
		   width: 28px;
		   height: 26px;
		   margin-right: 10px;
	   }
	}
	.btnDisable{
		background:rgba(49,125,255,0.4);
		color:#FFFFFF;
		margin-top: 20px;
	}
}
.enter-icon {
    background-image: url(../../assets/img/<EMAIL>);
	background-repeat: no-repeat;
    background-size: 14px 26px;
    height: 26px;
    width: 14px;
}

.custom-modal-body {
	position: absolute;
		top: 15%;
		left: 50px;
		right: 50px;
		border-radius: 8px;
		background-image: url(../../assets/img/errorBg.png);
		background-repeat: no-repeat;
		background-size: 100%;
		height: 700px;
}


.sign-add-btn {
	width: 240px;
	height: 62px;
	margin-bottom: 20px;
	margin-left: 30px;
	margin-top: 20px;
	text-align: center;
	line-height: 0.826667rem;
	font-size: 30px;
}

.btn-primay {
	color: white;
    padding: 0.1rem 0.4rem 0.1rem;
    display: inline-block;
    background-color: #317DFF;
    border-radius: 8px;
}
.btn-plain-circle {
	color: #317DFF;
	display: inline-block;
	background-image: url(../../assets/img/bcircle.png);
	background-repeat: no-repeat;
	background-size: 100% 0.826667rem;
}
.nodata {
	margin-top: 75px;
	text-align: center;
	img {
		width: 445px;
	}
	div {
		margin-top: 40px;
		color: #A0A0A0;
		font-size:32px;
	}
}
.search {
	height: 64px;
	position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background: #F6F6F7;
}

// .searchbar {
//     background-color: #F6F6F7;
//     padding: 0 30px;
//     display: flex;
//     align-items: center;
//     position: absolute;
//     top: 0;
//     width: calc(100% - 60px);
//     height: 120px;
// }
// .searchbar-inner {
//     background: white;
//     display: flex;
//     flex: 1;
//     align-items: center;
//     border-radius: 44px;
//     padding: 14px 0px 14px 15px;
// }
// .searchbar-inner-location {
//     color:#333333;
//     font-size: 32px;
//     flex: 0 0 80px;
//     border-right: 2px solid #E4E3E3;
//     margin-left: 20px;
//     margin-right: 10px;
    
// }

// .search-input-con {
//     flex: 1;
//     display: flex;
//     align-items: center;
// }
// .search-input {
//     color: #777777;
//     font-size: 30px;
//     box-sizing: border-box;
//     width: 100%;
//     height: 100%;
//     border: none;
//     // border-radius: 4px;
//     padding: 10px;
//     border-radius: 44px;
//     &:focus {
//     outline: none;
// }
// }
// .search-input-icon {
//     width: 32px;
//     height: 33px;
//     background-image: url('~@/assets/img/<EMAIL>');
//     background-repeat: no-repeat;
//     background-size: 100%;
//     margin-left: 10px;
// }
// .search-input-icon-right {
//     width: 32px;
//     height: 32px;
//     background: url('~@/assets/img/<EMAIL>') no-repeat center center;
//     background-size: 32px 32px;
//     padding-right: 10px;
//     margin-right: 20px;
// }
</style>
