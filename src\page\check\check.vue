<template>
    <div
        class="c"
        ref="pageC"
        :style="{ overflow: $store.getters.getHide ? 'hidden' : 'auto' }"
    >
        <h1 class="h1-1 h1-1z" @click="test">检查结果</h1>
        <div class="h1-box"></div>
        <div
            class="cli"
            v-for="(item, index) in checkList"
            :key="index"
            :id="'cli' + (index + 1)"
        >
            <check-item
                :checkData="item"
                :keyword="'c' + index"
                :checkId="$route.query.checkId"
                :isModified="modify"
                v-if="$route.query.checkId"
                :keyNum="index + 1"
            ></check-item>
        </div>
        <h1>备注信息</h1>
        <div class="c4">
            <ul>
                <li v-for="(data, index) in remarkPhoto" :key="'i' + index">
                    <!-- 视频文件：显示默认缩略图和播放按钮 -->
                    <template v-if="data.imgFileId && data.imgFileUrl">
                        <img
                            class="i1"
                            :src="data.imgFileUrl"
                            alt=""
                            data-who="video"
                        />
                        <div
                            class="i1-play"
                            @click="scanVideo(data.fileUrl, data.imgFileUrl)"
                        ></div>
                    </template>
                    <!-- 普通图片 -->
                    <img
                        class="i1"
                        :src="data.fileUrl"
                        alt=""
                        v-else
                        data-who="img"
                    />
                    <span @click="deleteImg(index)"></span>
                </li>
                <li v-if="remarkPhoto && remarkPhoto.length < 3">
                    <label @click="SelectImgOrVideoFn"> </label>
                </li>
            </ul>
            <div class="textarea">
                <textarea
                    v-reset-page
                    v-model="checkInfo"
                    placeholder="请输入相关信息..."
                ></textarea>
            </div>
            <span>{{ getFontNum(checkInfo) }}/200</span>
        </div>
        <div class="c5">
            <div class="c5lt">两定机构签字确认</div>
            <div class="c5rt" @click="sign">
                对结果{{ $store.getters.getSign ? "已确认" : "确认无误" }}
            </div>
        </div>
        <div class="c6">
            <div class="c6lt">
                <v-button type="primary" @click="submit1" size="large"
                    ><span slot="text">保存到草稿箱</span></v-button
                >
            </div>
            <div class="c6rt">
                <v-button type="default" @click="submit2" size="large"
                    ><span slot="text">提交</span></v-button
                >
            </div>
        </div>
        <check-sign ref="ckSign"></check-sign>
        <div class="select-two" v-show="showPhoto">
            <div class="st-mask" @click="closeSelectImg"></div>
            <div class="st-content">
                <div class="st-i">
                    <!-- <div class="st-ic" @click="useCameraFn">
                        <label for="upImg">
                            <input
                                type="file"
                                id="upImg"
                                accept="image/*"
                                @change="clickUpload($event)"
                                v-if="!isiOS"
                            />
                            <input
                                type="file"
                                id="upImg"
                                accept="image/*"
                                @change="clickUpload($event)"
                                v-else
                            />
                        </label>
                        <p class="p1">图片</p>
                    </div> -->
                    <div class="st-ic" @click="YSSC">
                        <img
                            src="../../assets/img/<EMAIL>"
                            height="60"
                            alt=""
                        />
                        <p class="p1">图片</p>
                    </div>
                    <div class="st-ic st-icrt" @click="useVideoFn">
                        <img src="../../assets/img/<EMAIL>" alt="" />
                        <p class="p2">视频</p>
                    </div>
                </div>
            </div>
        </div>
        <div class="vi" v-show="showVideos">
            <div class="vi-mask"></div>
            <div class="vi-content">
                <div class="vic-bg" @click="closeVideoBox"></div>
                <div class="vi-main">
                    <div ref="video"></div>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import vRadio from "../../components/v-radio/radio";
import vButton from "../../components/v-button/button";
import checkItem from "./check-item";
import {
    initCookie,
    queryCheckList,
    saveCheckDetail,
    updateCheck,
    queryCheckDetailList,
    upload,
    getFileToken,
} from "../../api/check";
import checkSign from "./checkSign";
// import 'dplayer/dist/DPlayer.min.css';
import DPlayer from "dplayer";
import lrz from "lrz";
import { getStoredUserLocation, closeWebview, selectVideo } from '@/assets/js/until.js';
import defaultVideoThumb from '../../assets/img/video.jpg';
export default {
    data() {
        return {
            fontNum: 0,
            checkInfo: "",
            checkList: [],
            check: [],
            completeLongitude: "",
            completeLatitude: "",
            // true:草稿箱
            modify: false,
            // 备注 上传图片和视频数组
            remarkPhoto: [],
            // 显示上传图片的弹窗
            showPhoto: false,
            // 预览视频弹窗
            showVideos: false,
        };
    },
    created() {
        // 判断移动终端
        var u = navigator.userAgent;
        this.isiOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/); //ios终端
        // this.$result(true, '提交成功！');
        if (this.$route.query.type && this.$route.query.type === "drafts") {
            // 草稿箱修改
            this.$loading(true, "");
            this.draftsFn();
        } else {
            // 检查页面
            this.$loading(true, "");
            this.queryCheckListFn();
        }
    },
    mounted() {
        console.log("🚀 页面加载完成");
        console.log("🔍 路由参数 checkId=", this.$route.query.checkId);
        console.log("🔍 完整路由参数=", this.$route.query);
        console.log("🔍 当前路由路径=", this.$route.path);
    },
    methods: {
        test() {
            console.log("===================");
            console.log(
                "_this.$store.getters.getUploadImgs=",
                this.$store.getters.getUploadImgs
            );
        },
        testInit(callback) {
            initCookie({ authCode: "11111111" }).then((result) => {
                console.log("initCookie", result);
                if (result.errorCode === "0") {
                    callback();
                }
            });
        },
        // 初始化，网页版不需要认证，直接执行回调
        initCookiefn(callback) {
            // 网页版不需要认证，直接执行业务逻辑
            if (callback && typeof callback === 'function') {
                callback();
            }
        },
        sign() {
            this.$refs.ckSign.open();
            this.$store.commit("setHide", true);
        },
        // 保存草稿箱checkStatus：1签到（草稿箱） 2检查完成（提交）
        // 传参有问题,拆分成两个不用传参的函数，
        // 两个接口操作基本相同
        // 'checkId': 签到后接口返回,
        // 'orgConfirm': 签名string,
        // 'checkComment': 备注,
        // checkStatus：1签到（草稿箱） 2检查完成（提交）
        // 'completeLongitude': '',
        // 'completeLatitude': ''
        submit1() {
            const _this = this;
            console.log(
                "定位 _this.completeLongitude=",
                _this.completeLongitude
            );

            // 检查必要参数
            const checkId = _this.$route.query.checkId;
            if (!checkId) {
                _this.$toast.center("缺少检查ID参数");
                return;
            }

            if (_this.completeLongitude && _this.completeLatitude) {
                _this.$loading(true, "");

                // ============== 调试打印：开始 ==============
                console.log("==================== 草稿箱提交验证 ====================");
                console.log("📋 总检查项目列表 checkList:", _this.checkList);
                console.log("📋 检查项目数量:", _this.checkList.length);

                // checkStatus:是|否   选项必填,不能为null
                // 获取list有值和没有值的数组
                const storeUploadImgs = _this.$store.getters.getUploadImgs;
                console.log("💾 Store中的uploadImgs:", storeUploadImgs);
                console.log("💾 uploadImgs数量:", storeUploadImgs ? storeUploadImgs.length : 0);

                if (!storeUploadImgs || storeUploadImgs.length === 0) {
                    console.log("❌ Store中没有任何检查项目数据！");
                    console.log("预期应该有", _this.checkList.length, "个检查项");
                    _this.$loading(false, "");
                    _this.$toast.center("请先完成检查项目选择");
                    return;
                }

                let uploadImg = _this._distinctUploadImg(storeUploadImgs);
                console.log("🔍 去重后的uploadImg:", uploadImg);
                console.log("🔍 去重后数量:", uploadImg.length);

                // hasVal:2--可传值，但空值
                let unDeal = uploadImg.find(function (v) {
                    return v.checkStatus === null;
                });

                console.log("🔍 未选择的项目列表:");
                uploadImg.forEach((item, index) => {
                    if (item.checkStatus === null) {
                        console.log(`  ❌ 第${index + 1}项未选择:`, {
                            listId: item.listId,
                            checkId: item.checkId,
                            checkStatus: item.checkStatus,
                            hasVal: item.hasVal,
                            checkPhotos: item.checkPhotos
                        });
                        // 找到对应的检查项目名称
                        const checkItem = _this.checkList.find(c => c.listId === item.listId);
                        if (checkItem) {
                            console.log(`     检查项目名称: ${checkItem.checkTip}`);
                        }
                    } else {
                        console.log(`  ✅ 第${index + 1}项已选择:`, {
                            listId: item.listId,
                            checkStatus: item.checkStatus === '1' ? '是' : '否'
                        });
                    }
                });

                console.log("第一个未选中项目", unDeal);
                console.log("==================== 验证结束 ====================");
                if (!unDeal) {
                    // 组装参数
                    let uploadImgParam = _this.$store.getters.getUploadImgs.map(
                        (el) => {
                            let fileIdArr = [];
                            if (el.checkPhotos.length > 0) {
                                el.checkPhotos.forEach((file) => {
                                    // 区分图片和视频的处理方式
                                    if (file.imgFileId && file.imgFileId > 0) {
                                        // 视频（有真实缩略图）：拼接格式 "缩略图ID|视频ID"
                                        fileIdArr.push(file.imgFileId + "|" + file.fileId);
                                        console.log(`📹 检查项目视频文件（有缩略图）: ${file.imgFileId}|${file.fileId}`);
                                    } else if (file.imgFileId === -1) {
                                        // 视频（默认缩略图）：只传视频ID
                                        fileIdArr.push(file.fileId);
                                        console.log(`📹 检查项目视频文件（默认缩略图）: ${file.fileId}`);
                                    } else {
                                        // 图片：只传文件ID
                                        fileIdArr.push(file.fileId);
                                        console.log(`📷 检查项目图片文件: ${file.fileId}`);
                                    }
                                });
                            }
                            console.log(`🔍 检查项目 ${el.listId} 文件处理结果:`, fileIdArr);
                            return {
                                checkId: el.checkId,
                                listId: el.listId,
                                checkPhotos: fileIdArr.join(),
                                checkStatus: el.checkStatus,
                            };
                        }
                    );
                    // 备注组装参数
                    let remarkPhoto = _this.remarkPhoto.map((el) => {
                        // 区分图片和视频的处理方式
                        if (el.imgFileId && el.imgFileId > 0) {
                            // 视频（有真实缩略图）：拼接格式 "缩略图ID|视频ID"
                            console.log(`📹 备注视频文件（有缩略图）: ${el.imgFileId}|${el.fileId}`);
                            return el.imgFileId + "|" + el.fileId;
                        } else if (el.imgFileId === -1) {
                            // 视频（默认缩略图）：只传视频ID
                            console.log(`📹 备注视频文件（默认缩略图）: ${el.fileId}`);
                            return el.fileId;
                        } else {
                            // 图片：只传文件ID
                            console.log(`📷 备注图片文件: ${el.fileId}`);
                            return el.fileId;
                        }
                    }).filter(id => id !== null); // 过滤掉无效数据
                    console.log("🔍 备注照片处理结果:", remarkPhoto);
                    console.log(
                        "草稿箱 saveCheckDetail param=",
                        uploadImgParam
                    );
                    saveCheckDetail(uploadImgParam)
                        .then((signRes) => {
                            console.log("草稿箱 saveCheckDetail res=", signRes);
                            if (signRes.errorCode === "0") {
                                // 检查签名成功后才能调updateCheck
                                const param = {
                                    checkId: _this.$route.query.checkId,
                                    orgConfirm: _this.$store.getters.getSign,
                                    checkComment: _this.checkInfo,
                                    commentPhotos: remarkPhoto.join(),
                                    checkStatus: "1",
                                    completeLongitude: _this.completeLongitude,
                                    completeLatitude: _this.completeLatitude,
                                };
                                console.log("updateCheck param", param);
                                updateCheck(param)
                                    .then((res) => {
                                        _this.$loading(false, "");
                                        console.log("updateCheck res==", res);
                                        if (res.errorCode === "0") {
                                            _this.$store.commit(
                                                "deleteUploadImgs",
                                                true
                                            );
                                            _this.$result(true, "保存成功！");
                                            setTimeout(() => {
                                                _this.$router.replace({
                                                    path: "/historyRecord",
                                                    query: {
                                                        type: "drafts",
                                                    },
                                                });
                                            }, 2000);
                                        } else if (res.errorCode === "1003") {
                                            _this.initCookiefn(_this.submit1);
                                            // _this.testInit(_this.submit1)
                                        } else {
                                            _this.$result(false, "保存失败！");
                                        }
                                    })
                                    .catch((error) => {
                                        _this.$toast.center("接口异常");
                                    });
                            } else if (signRes.errorCode === "1003") {
                                _this.initCookiefn(_this.submit1);
                                // _this.testInit(_this.submit1)
                            } else {
                                _this.$toast.center(signRes.value);
                            }
                        })
                        .catch((error) => {
                            this.$toast.center("接口异常");
                        });
                } else {
                    _this.$loading(false, "");
                    let UnDealPos = document
                        .querySelector("#cli" + unDeal.listId)
                        .getBoundingClientRect();
                    _this.$refs.pageC.scrollTo({
                        top: _this.$refs.pageC.scrollTop + UnDealPos.top,
                    });
                    // _this.$toast.center('请选择检查结果');
                }
            } else {
                _this.$toast.center("无法定位");
            }
        },
        // 提交
        submit2() {
            const _this = this;
            console.log(
                "定位 _this.completeLongitude=",
                _this.completeLongitude
            );

            // 检查必要参数
            const checkId = _this.$route.query.checkId;
            if (!checkId) {
                _this.$toast.center("缺少检查ID参数");
                return;
            }

            if (_this.completeLongitude && _this.completeLatitude) {
                // ============== 调试打印：开始 ==============
                console.log("==================== 提交验证 ====================");
                console.log("📋 总检查项目列表 checkList:", _this.checkList);
                console.log("📋 检查项目数量:", _this.checkList.length);

                // checkStatus:是|否   选项必填,不能为null
                // （为做两个按钮同时上传图片做的，现在只支持单个按钮上传）获取list有值和没有值的数组
                const storeUploadImgs = _this.$store.getters.getUploadImgs;
                console.log("💾 Store中的uploadImgs:", storeUploadImgs);
                console.log("💾 uploadImgs数量:", storeUploadImgs ? storeUploadImgs.length : 0);

                if (!storeUploadImgs || storeUploadImgs.length === 0) {
                    console.log("❌ Store中没有任何检查项目数据！");
                    console.log("预期应该有", _this.checkList.length, "个检查项");
                    _this.$toast.center("请先完成检查项目选择");
                    return;
                }

                let uploadImg = _this._distinctUploadImg(storeUploadImgs);
                console.log("🔍 去重后的uploadImg:", uploadImg);
                console.log("🔍 去重后数量:", uploadImg.length);

                // hasVal:2--可传值，但空值
                let unDeal = uploadImg.find(function (v) {
                    return v.checkStatus === null;
                });

                console.log("🔍 项目选择详情:");
                uploadImg.forEach((item, index) => {
                    if (item.checkStatus === null) {
                        console.log(`  ❌ 第${index + 1}项未选择:`, {
                            listId: item.listId,
                            checkId: item.checkId,
                            checkStatus: item.checkStatus,
                            hasVal: item.hasVal,
                            checkPhotos: item.checkPhotos
                        });
                        // 找到对应的检查项目名称
                        const checkItem = _this.checkList.find(c => c.listId === item.listId);
                        if (checkItem) {
                            console.log(`     检查项目名称: ${checkItem.checkTip}`);
                        }
                    } else {
                        console.log(`  ✅ 第${index + 1}项已选择:`, {
                            listId: item.listId,
                            checkStatus: item.checkStatus === '1' ? '是' : '否',
                            hasVal: item.hasVal,
                            照片数量: item.checkPhotos ? item.checkPhotos.length : 0
                        });
                    }
                });

                console.log("第一个未选中项目", unDeal);
                console.log("==================== 验证结束 ====================");
                if (!unDeal) {
                    // 组装参数
                    let uploadImgParam = _this.$store.getters.getUploadImgs.map(
                        (el) => {
                            let fileIdArr = [];
                            if (el.checkPhotos.length > 0) {
                                el.checkPhotos.forEach((file) => {
                                    // 区分图片和视频的处理方式
                                    if (file.imgFileId && file.imgFileId > 0) {
                                        // 视频（有真实缩略图）：拼接格式 "缩略图ID|视频ID"
                                        fileIdArr.push(file.imgFileId + "|" + file.fileId);
                                        console.log(`📹 检查项目视频文件（有缩略图）: ${file.imgFileId}|${file.fileId}`);
                                    } else if (file.imgFileId === -1) {
                                        // 视频（默认缩略图）：只传视频ID
                                        fileIdArr.push(file.fileId);
                                        console.log(`📹 检查项目视频文件（默认缩略图）: ${file.fileId}`);
                                    } else {
                                        // 图片：只传文件ID
                                        fileIdArr.push(file.fileId);
                                        console.log(`📷 检查项目图片文件: ${file.fileId}`);
                                    }
                                });
                            }
                            console.log(`🔍 检查项目 ${el.listId} 文件处理结果:`, fileIdArr);
                            return {
                                checkId: el.checkId,
                                listId: el.listId,
                                checkPhotos: fileIdArr.join(),
                                checkStatus: el.checkStatus,
                            };
                        }
                    );
                    // 备注组装参数
                    let remarkPhoto = _this.remarkPhoto.map((el) => {
                        // 区分图片和视频的处理方式
                        if (el.imgFileId && el.imgFileId > 0) {
                            // 视频（有真实缩略图）：拼接格式 "缩略图ID|视频ID"
                            console.log(`📹 备注视频文件（有缩略图）: ${el.imgFileId}|${el.fileId}`);
                            return el.imgFileId + "|" + el.fileId;
                        } else if (el.imgFileId === -1) {
                            // 视频（默认缩略图）：只传视频ID
                            console.log(`📹 备注视频文件（默认缩略图）: ${el.fileId}`);
                            return el.fileId;
                        } else {
                            // 图片：只传文件ID
                            console.log(`📷 备注图片文件: ${el.fileId}`);
                            return el.fileId;
                        }
                    }).filter(id => id !== null); // 过滤掉无效数据
                    console.log("🔍 备注照片处理结果:", remarkPhoto);
                    if (_this.$store.getters.getSign) {
                        console.log("已签名");
                        console.log(
                            "提交 saveCheckDetail param=",
                            uploadImgParam
                        );
                        saveCheckDetail(uploadImgParam)
                            .then((signRes) => {
                                console.log("提交 saveCheckDetail=", signRes);
                                if (signRes.errorCode === "0") {
                                    const param = {
                                        checkId: _this.$route.query.checkId,
                                        orgConfirm:
                                            _this.$store.getters.getSign,
                                        checkComment: _this.checkInfo,
                                        commentPhotos: remarkPhoto.join(),
                                        checkStatus: "2",
                                        completeLongitude:
                                            _this.completeLongitude,
                                        completeLatitude:
                                            _this.completeLatitude,
                                    };
                                    console.log("updateCheck param", param);
                                    updateCheck(param)
                                        .then((res) => {
                                            console.log("updateCheck==", res);
                                            if (res.errorCode === "0") {
                                                _this.$store.commit(
                                                    "deleteUploadImgs",
                                                    true
                                                );
                                                _this.$result(
                                                    true,
                                                    "提交成功！"
                                                );
                                                setTimeout(() => {
                                                    _this.$router.replace({
                                                        path: "/historyRecord",
                                                        query: {
                                                            type: "checkList",
                                                        },
                                                    });
                                                }, 2000);
                                            } else if (
                                                res.errorCode === "1003"
                                            ) {
                                                _this.initCookiefn(
                                                    _this.submit2
                                                );
                                                // _this.testInit(_this.submit2)
                                            } else {
                                                _this.$result(
                                                    false,
                                                    "提交失败！"
                                                );
                                            }
                                        })
                                        .catch((error) => {
                                            _this.$toast.center("接口异常");
                                        });
                                } else if (signRes.errorCode === "1003") {
                                    _this.initCookiefn(_this.submit2);
                                    // _this.testInit(_this.submit2)
                                } else {
                                    _this.$toast.center(signRes.value);
                                }
                            })
                            .catch((error) => {
                                _this.$toast.center("接口异常");
                            });
                    } else {
                        console.log("未签名");
                        _this.$confirmBox({
                            title: "提交",
                            content:
                                "两定机构工作人员对检查结果存疑，未做签字确认。",
                            callback: function () {
                                console.log(
                                    "提交 saveCheckDetail param=",
                                    uploadImgParam
                                );
                                saveCheckDetail(uploadImgParam)
                                    .then((signRes) => {
                                        console.log(
                                            "提交 saveCheckDetail=",
                                            signRes
                                        );
                                        if (signRes.errorCode === "0") {
                                            const param = {
                                                checkId:
                                                    _this.$route.query.checkId,
                                                orgConfirm:
                                                    _this.$store.getters
                                                        .getSign,
                                                checkComment: _this.checkInfo,
                                                checkStatus: "2",
                                                completeLongitude:
                                                    _this.completeLongitude,
                                                completeLatitude:
                                                    _this.completeLatitude,
                                            };
                                            console.log(
                                                "updateCheck param",
                                                param
                                            );
                                            updateCheck(param)
                                                .then((res) => {
                                                    console.log(
                                                        "updateCheck==",
                                                        res
                                                    );
                                                    if (res.errorCode === "0") {
                                                        _this.$store.commit(
                                                            "deleteUploadImgs",
                                                            true
                                                        );
                                                        _this.$result(
                                                            true,
                                                            "提交成功！"
                                                        );
                                                        setTimeout(() => {
                                                            _this.$router.replace(
                                                                {
                                                                    path: "/historyRecord",
                                                                }
                                                            );
                                                        }, 2000);
                                                    } else if (
                                                        res.errorCode === "1003"
                                                    ) {
                                                        _this.initCookiefn(
                                                            _this.submit2
                                                        );
                                                        // _this.testInit(_this.submit2)
                                                    } else {
                                                        _this.$result(
                                                            false,
                                                            "提交失败！"
                                                        );
                                                    }
                                                })
                                                .catch((error) => {
                                                    _this.$toast.center(
                                                        "接口异常"
                                                    );
                                                });
                                        } else if (
                                            signRes.errorCode === "1003"
                                        ) {
                                            _this.initCookiefn(_this.submit2);
                                            // _this.testInit(_this.submit2)
                                        } else {
                                            _this.$toast.center(signRes.value);
                                        }
                                    })
                                    .catch((error) => {
                                        _this.$toast.center("接口异常");
                                    });
                            },
                            openCkeckSign: function () {
                                // console.log('去签名 check');
                                _this.sign();
                            },
                        });
                    }
                } else {
                    let UnDealPos = document
                        .querySelector("#cli" + unDeal.listId)
                        .getBoundingClientRect();
                    _this.$refs.pageC.scrollTo({
                        top: _this.$refs.pageC.scrollTop + UnDealPos.top,
                    });
                    // _this.$toast.center('请拍照片或录视频，或者选择否或是');
                }
            } else {
                _this.$toast.center("无法定位");
            }
        },
        // 获取检查项目列表,同时获取经纬度
        // 检查列表相关查询接口返回新增数值型字段picType，枚举值如下：1-是+图片  2-否+图片  3-是否都可以加图片  4-是否都不需要图片
        queryCheckListFn() {
            const _this = this;
            // 获取定位
            _this.getLocation();

            // 检查checkId参数
            const checkId = _this.$route.query.checkId;
            if (!checkId) {
                _this.$loading(false, "");
                _this.$toast.center("缺少检查ID参数");
                return;
            }

            queryCheckList({ checkId: checkId })
                .then((res) => {
                    console.log("🔍 queryCheckList 完整响应=", res);
                    console.log("🔍 checkId参数=", checkId);
                    console.log("🔍 响应状态码=", res.errorCode);
                    console.log("🔍 响应数据=", res.data);

                    _this.$loading(false, "");
                    if (res.errorCode === "0") {
                        if (res.data && res.data.length > 0) {
                            _this.checkList = res.data.sort((a, b) => {
                                return a.orderNo - b.orderNo;
                            });
                            console.log("✅ 检查项目列表加载成功，数量:", _this.checkList.length);
                            console.log("✅ checkList详细数据=", _this.checkList);
                        } else {
                            _this.checkList = [];
                            console.log("⚠️ 接口返回成功但数据为空");
                            _this.$toast.center("该检查任务暂无检查项目");
                        }
                    } else if (res.errorCode === "1003") {
                        console.log("🔄 需要重新认证");
                        _this.initCookiefn(_this.queryCheckListFn);
                    } else {
                        console.log("❌ 接口返回错误:", res.value);
                        _this.$toast.center(res.value);
                    }
                })
                .catch((error) => {
                    _this.$loading(false, "");
                    _this.$toast.center("接口异常");
                });
        },
        async getLocation() {
            try {
                // 读取已获取并缓存的位置信息（首页已写入 localStorage）
                const res = getStoredUserLocation() || {};
                const fallback = { longitude: 120.14738921730009, latitude: 30.244121753904395 };
                const { longitude, latitude } = (res.location && typeof res.location.longitude !== 'undefined')
                    ? { longitude: Number(res.location.longitude), latitude: Number(res.location.latitude) }
                    : fallback;
                console.log('位置信息(本地):', longitude, latitude);
                this.completeLongitude = longitude;
                this.completeLatitude = latitude;
            } catch (error) {
                console.error('获取位置信息异常:', error);
                // 本地无数据则给默认位置
                this.completeLongitude = 120.14738921730009;
                this.completeLatitude = 30.244121753904395;
            }
        },
        draftsFn() {
            const _this = this;
            // 获取定位
            _this.getLocation();
            if (_this.$route.query.checkId) {
                const param = {
                    checkId: _this.$route.query.checkId,
                };
                queryCheckDetailList(param).then((res) => {
                    console.log("queryCheckDetailList drafts=", res);
                    _this.$loading(false, "");
                    if (res.errorCode === "0") {
                        if (res.data.checkDetailList.length > 0) {
                            _this.checkList = res.data.checkDetailList.sort(
                                (a, b) => {
                                    return a.orderNo - b.orderNo;
                                }
                            );
                            console.log(
                                "草稿箱data _this.checkList sort=",
                                _this.checkList
                            );
                        } else {
                            _this.checkList = [];
                        }
                        // _this.checkList = res.data.checkDetailList;
                        var check = res.data.check;
                        _this.checkInfo = check.checkComment;
                        if (
                            typeof res.data.check.commentPhotos === "string" &&
                            res.data.check.commentPhotos !== ""
                        ) {
                            let commentPhotos = JSON.parse(res.data.check.commentPhotos);
                            console.log('🔍 草稿箱加载备注照片数据:', commentPhotos);

                            _this.remarkPhoto = commentPhotos.map((v) => {
                                // 区分图片和视频：通过文件扩展名判断
                                let fileData;
                                const fileUrl = v.fileUrl || '';
                                const isVideo = /\.(mp4|avi|mov|wmv|flv|webm|mkv)$/i.test(fileUrl);

                                if (v.imgFileId !== undefined || isVideo) {
                                    // 视频文件：使用默认缩略图
                                    fileData = {
                                        fileId: v.fileId,
                                        fileUrl: v.fileUrl,
                                        imgFileId: -1, // 视频使用默认缩略图标识
                                        imgFileUrl: defaultVideoThumb
                                    };
                                    console.log('📁 处理备注视频文件:', {
                                        fileId: v.fileId,
                                        fileUrl: v.fileUrl,
                                        判断依据: v.imgFileId !== undefined ? 'imgFileId字段' : '文件扩展名'
                                    });
                                } else {
                                    // 图片文件：不设置imgFileId和imgFileUrl
                                    fileData = {
                                        fileId: v.fileId,
                                        fileUrl: v.fileUrl
                                        // 图片不设置 imgFileId 和 imgFileUrl
                                    };
                                    console.log('📁 处理备注图片文件:', {
                                        fileId: v.fileId,
                                        fileUrl: v.fileUrl
                                    });
                                }

                                return fileData;
                            });
                        } else {
                            _this.remarkPhoto = [];
                        }
                        // console.log('_this.remarkPhoto===', _this.remarkPhoto);
                        _this.modify = true;
                        _this.$refs.ckSign.showImg(check.orgConfirm);
                        _this.$store.commit("setSign", check.orgConfirm);
                    } else if (res.errorCode === "1003") {
                        _this.initCookiefn(_this.draftsFn);
                        // _this.testInit(_this.draftsFn)
                    } else {
                        _this.$toast.center(res.value);
                    }
                });
            } else {
                _this.$loading(false, "");
                _this.$toast.center("checkId不存在");
            }
        },
        getFontNum(v) {
            if (v) {
                return v.length;
            } else {
                return 0;
            }
        },
        _orderArr(arr) {
            return arr.sort((a, b) => {
                return a - b;
            });
        },

        YSSC() {
            // H5图片选择
            const _this = this;
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = 'image/*';
            input.multiple = true; // 允许多选

            input.onchange = (e) => {
                const files = e.target.files;
                if (!files.length) return;

                _this.$loading(true, "正在上传图片...");

                // 处理所有选中的图片
                let uploadPromises = [];
                for (let i = 0; i < files.length; i++) {
                    uploadPromises.push(_this.uploadSingleImage(files[i]));
                }

                Promise.all(uploadPromises).then(() => {
                    _this.$loading(false, "");
                    _this.showPhoto = false;
                    _this.$toast.center('图片上传成功');
                }).catch((error) => {
                    console.error('图片上传失败', error);
                    _this.$loading(false, "");
                    _this.showPhoto = false;
                    _this.$toast.center('部分图片上传失败');
                });
            };

            input.click();
        },
        // 上传单张图片
        uploadSingleImage(file) {
            const _this = this;
            return new Promise((resolve, reject) => {
                // 使用 lrz 压缩图片
                lrz(file, {
                    width: 1000,
                    quality: 0.5,
                }).then(function (rst) {
                    return getFileToken();
                }).then((token) => {
                    if (token.errorCode.toString() !== "0") {
                        reject(new Error('获取token失败'));
                        return;
                    }

                    const fd = new FormData();
                    fd.append("file", file);
                    fd.append("fileToken", token.data);

                    return upload(fd);
                }).then((res) => {
                    if (res.errorCode.toString() === "0") {
                        // 备注图片上传成功，设置为图片格式（不设置imgFileId和imgFileUrl）
                        _this.remarkPhoto.push({
                            fileUrl: res.data.fileUrl,
                            fileId: res.data.fileId,
                            // 图片不设置 imgFileId 和 imgFileUrl，这样模板会显示为图片
                        });
                        console.log('📷 备注图片上传成功:', {
                            fileId: res.data.fileId,
                            fileUrl: res.data.fileUrl,
                            类型: '图片'
                        });
                        resolve(res);
                    } else {
                        reject(new Error(res.value));
                    }
                }).catch((error) => {
                    console.error('单张图片上传失败', error);
                    reject(error);
                });
            });
        },
        clickUpload(e) {
            const _this = this;
            _this.$loading(true, "");
            var files = e.target.files || e.dataTransfer.files;
            // console.log('files', files);
            if (!files.length) return;
            lrz(files[0], {
                width: 1000,
                quality: 0.5,
            }).then(function (rst) {
                // _this.imgList.push(rst.base64);
                _this.uploadFn(rst.file);
                return rst;
            });
        },
        deleteImg(i) {
            // this.imgList.splice(i, 1);
            this.remarkPhoto.splice(i, 1);
        },
        // 上传图片
        uploadFn(file) {
            const _this = this;
            getFileToken()
                .then((token) => {
                    console.log("getFileToken=", token);
                    if (token.errorCode.toString() === "0") {
                        const fd = new FormData();
                        fd.append("file", file);
                        fd.append("fileToken", token.data);
                        upload(fd)
                            .then((res) => {
                                console.log("upload res=", res);
                                _this.$loading(false, "");
                                if (res.errorCode.toString() === "0") {
                                    // 备注图片上传成功，设置为图片格式（不设置imgFileId和imgFileUrl）
                                    _this.remarkPhoto.push({
                                        fileUrl: res.data.fileUrl,
                                        fileId: res.data.fileId,
                                        // 图片不设置 imgFileId 和 imgFileUrl，这样模板会显示为图片
                                    });
                                    console.log('📷 备注图片上传成功(方式2):', {
                                        fileId: res.data.fileId,
                                        fileUrl: res.data.fileUrl,
                                        类型: '图片'
                                    });
                                } else {
                                    _this.$toast.center(res.value);
                                }
                            })
                            .catch((error) => {
                                _this.$toast.center("接口异常");
                            });
                    } else if (token.errorCode.toString() === "1003") {
                        _this.initCookiefn(_this.uploadFn);
                        // _this.testInit(_this.uploadFn)
                    } else {
                        _this.$toast.center(token.value);
                    }
                })
                .catch((error) => {
                    _this.$toast.center("接口异常");
                });
        },
        // 选择上传 视频 或 图片
        SelectImgOrVideoFn() {
            this.showPhoto = true;
        },
        closeSelectImg() {
            this.showPhoto = false;
        },
        useCameraFn() {
            // 关闭弹窗
            this.showPhoto = false;
        },
        /*
         * H5选择视频并上传
         */
        async useVideoFn() {
            const _this = this;
            try {
                // 使用H5选择视频
                const videoData = await selectVideo();
                console.log('选择视频成功', videoData);

                _this.$loading(true, '正在上传视频...');

                // 获取上传token
                const token = await getFileToken();
                if (token.errorCode.toString() !== "0") {
                    if (token.errorCode.toString() === "1003") {
                        _this.initCookiefn(_this.useVideoFn);
                    } else {
                        _this.$toast.center(token.value);
                    }
                    _this.$loading(false, '');
                    return;
                }

                // 构建上传FormData
                const formData = new FormData();
                formData.append('file', videoData.file);
                formData.append('fileToken', token.data);

                // 上传视频
                let saveUrl = window.location.origin + "/medical-supervision/file/uploadVedio";

                const response = await fetch(saveUrl, {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();
                console.log('🎥 备注视频上传接口返回结果:', result);
                console.log('🔍 result.data 详细结构:', result.data);
                console.log('🔍 result.data 所有字段:', Object.keys(result.data || {}));

                if (result.errorCode === '0' || result.errorCode === 0) {
                    // 直接使用后端返回的字段，统一使用默认缩略图
                    const videoData = {
                        fileUrl: result.data.fileUrl,
                        fileId: result.data.fileId,
                        imgFileId: -1, // 统一使用默认缩略图标识
                        imgFileUrl: defaultVideoThumb
                    };

                    console.log('🎥 备注视频上传成功，使用默认缩略图:', {
                        fileId: result.data.fileId,
                        fileUrl: result.data.fileUrl,
                        defaultThumb: defaultVideoThumb
                    });

                    // 添加到照片列表
                    _this.remarkPhoto.push(videoData);
                    _this.$toast.center('视频上传成功');
                } else {
                    _this.$toast.center(result.value || '上传失败');
                }

                _this.$loading(false, '');
                _this.showPhoto = false;
            } catch (error) {
                console.error('视频选择或上传失败', error);
                _this.$loading(false, '');
                _this.showPhoto = false;
                if (error.message !== '用户取消选择') {
                    _this.$toast.center('视频处理失败');
                }
            }
        },
        scanVideo(v, i) {
            console.log("预览视频url=", v);
            window.location.href = "https://mss.hfi-health.com:9443/" + v;
            return;
            const _this = this;
            _this.showVideos = true;
            _this.dp = new DPlayer({
                container: _this.$refs.video,
                video: {
                    url: v,
                    pic: i,
                    thumbnails: i,
                },
            });
        },
        closeVideoBox() {
            this.showVideos = false;
            this.dp.pause();
        },
        _distinctUploadImg(arr) {
            var result = [],
                json = {};
            for (var i = 0, len = arr.length; i < len; i++) {
                if (!json[arr[i]["listId"]]) {
                    json[arr[i]["listId"]] = 1;
                    result.push(arr[i]);
                } else {
                    if (arr[i]["hasVal"] === "1") {
                        result = result.map((v) => {
                            if (
                                arr[i].checkId === v.checkId &&
                                arr[i].listId === v.listId
                            ) {
                                v.hasVal = "1";
                            }
                            return v;
                        });
                    }
                }
            }
            return result;
        },
    },
    beforeRouteLeave(to, from, next) {
        if (to.name === "IdentityCheck") {
            next("/index");
        } else {
            next();
        }
    },
    beforeDestroy() {
        // console.log('beforeDestroy');
        this.$store.commit("deleteUploadImgs", true);
        this.$store.commit("setHide", false);
        this.$store.commit("setSign", "");
    },
    directives: {
        resetPage: {
            inserted: function (el) {
                document.body.addEventListener("focusout", () => {
                    if (/(iPhone|iPad|iPod|iOS)/i.test(navigator.userAgent)) {
                        //软键盘收起的事件处理
                        setTimeout(() => {
                            const scrollHeight =
                                document.documentElement.scrollTop ||
                                document.body.scrollTop ||
                                0;
                            window.scrollTo(0, Math.max(scrollHeight - 1, 0));
                        }, 100);
                    }
                });
            },
        },
    },
    components: {
        vButton,
        checkItem,
        checkSign,
    },
    watch: {
        checkInfo(nv, ov) {
            if (nv && nv.length > 200) {
                this.$toast.center("只能输入200个字");
                this.checkInfo = this.checkInfo.substr(0, 200);
            }
        },
    },
};
</script>
<style lang="less" scoped>
@import "../../assets/css/mixin.less";
.c {
    line-height: 1.5;
    font-weight: 500;
    overflow: auto;
    height: 100%;
    -webkit-overflow-scrolling: touch;
    h1 {
        position: relative;
        width: 100%;
        padding: 0 30px;
        height: 89px;
        line-height: 89px;
        font-size: 32px;
        color: #000;
        background: #fff;
        box-sizing: border-box;
    }
    h1.h1-1 {
        position: fixed;
        z-index: 10;
        &::after {
            .setBottomLine(#ECECEC);
        }
    }
    h1.h1-1z {
        position: fixed;
        z-index: 101;
        &::after {
            .setBottomLine(#ECECEC);
        }
    }
    .h1-box {
        width: 100%;
        height: 89px;
    }
    .cli {
        position: relative;
    }
    .c4 {
        position: relative;
        padding: 8px 30px 42px 30px;
        &::after {
            .setBottomLine(#ECECEC);
        }
        ul {
            padding: 36px 0 42px 0;
            display: flex;
            position: relative;
            li {
                position: relative;
                flex: 0 0 218px;
                padding-top: 218px;
                &:nth-child(1) {
                    margin-right: 18px;
                }
                &:nth-child(3) {
                    margin-left: 18px;
                }
                .i1 {
                    position: absolute;
                    left: 0;
                    top: 0;
                    width: 100%;
                    height: 100%;
                    border-radius: 6px;
                    z-index: 10;
                }
                .i1-play {
                    position: absolute;
                    left: 0;
                    top: 0;
                    width: 100%;
                    height: 100%;
                    z-index: 20;
                    background: url(../../assets/img/<EMAIL>) no-repeat
                        center center;
                    background-size: 81px 81px;
                }
                video {
                    position: absolute;
                    left: 0;
                    top: 0;
                    width: 208px;
                    height: 208px;
                }
                span {
                    display: block;
                    position: absolute;
                    right: 0;
                    top: 0;
                    width: 35px;
                    height: 35px;
                    background: url(../../assets/img/<EMAIL>) no-repeat
                        center center;
                    background-size: 35px 35px;
                    z-index: 30;
                }
                label {
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: url("../../assets/img/<EMAIL>") no-repeat
                        center center;
                    background-size: 100% 100%;
                    border-radius: 6px;
                    overflow: hidden;
                    input {
                        width: 1px;
                        height: 1px;
                        overflow: hidden;
                    }
                }
            }
        }
        .textarea {
            height: 136px;
            padding: 20px 20px 48px 20px;
            background-color: #f7f7f7;
            textarea {
                box-sizing: border-box;
                width: 100%;
                height: 136px;
                border: none;
                outline: none;
                font-weight: 500;
                line-height: 1.5;
                font-size: 32px;
                background-color: #f7f7f7;
                color: #333;
                &::placeholder {
                    font-weight: 500;
                    color: #777;
                }
            }
        }

        span {
            position: absolute;
            bottom: 50px;
            right: 50px;
            font-size: 32px;
            color: #777;
        }
    }
    .c5 {
        font-size: 32px;
        padding: 21px 30px 20px 30px;
        position: relative;
        display: flex;
        &::after {
            .setBottomLine(#ECECEC);
        }
        .c5lt {
            color: #333;
            flex: 1;
        }
        .c5rt {
            color: #777;
            flex: 1;
            text-align: right;
            padding-right: 42px;
            background: url(../../assets/img/<EMAIL>) no-repeat right
                center;
            background-size: 17px 30px;
        }
    }
    .c6 {
        padding: 88px 30px 50px;
        display: flex;
        .c6lt {
            flex: 1;
            margin-right: 10px;
            height: 98px;
        }
        .c6rt {
            flex: 1;
            margin-left: 10px;
            height: 98px;
        }
    }
    .select-two {
        position: fixed;
        bottom: 0;
        top: 0;
        left: 0;
        right: 0;
        z-index: 100;
        .st-mask {
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.2);
        }
        .st-content {
            position: absolute;
            bottom: 0;
            left: 0;
            width: calc(100% - 320px);
            padding: 0 160px;
            background: #fff;
            box-shadow: 0 2px 8px #317dff;
            overflow: hidden;
            .st-i {
                display: flex;
                .st-ic {
                    flex: 1;
                    text-align: center;
                    padding: 37px 0;
                    position: relative;
                    label {
                        display: block;
                        width: 116px;
                        height: 116px;
                        background: url("../../assets/img/<EMAIL>")
                            no-repeat center center;
                        background-size: 116px auto;
                        overflow: hidden;
                        input {
                            width: 1px;
                            height: 1px;
                            overflow: hidden;
                        }
                    }
                    p {
                        font-size: 32px;
                        color: #000;
                        font-weight: bold;
                        width: 116px;
                        text-align: center;
                        padding: 16px 0;
                    }
                    img {
                        display: block;
                        width: 116px;
                    }
                }
                .st-icrt {
                    text-align: right;
                    img {
                        display: inline-block;
                    }
                    p {
                        float: right;
                    }
                }
            }
        }
    }
    .vi {
        position: fixed;
        bottom: 0;
        top: 0;
        left: 0;
        right: 0;
        z-index: 100;
        .vi-mask {
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
        }
        .vi-content {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 100%;
            .vi-main {
                padding: 0 30px;
                position: absolute;
                top: 50%;
                left: 0;
                transform: translateY(-50%);
                width: calc(100% - 60px);
                z-index: 30;
                /deep/ .dplayer {
                    .dplayer-controller {
                        height: 41px; /*no*/
                        padding: 0 20px; /*no*/
                        .dplayer-icons.dplayer-comment-box {
                            height: 38px; /*no*/
                            left: 20px; /*no*/
                            right: 20px; /*no*/
                            .dplayer-icon {
                                padding: 7px; /*no*/
                                width: 40px; /*no*/
                            }
                        }
                        .dplayer-icons.dplayer-icons-left {
                            height: 38px; /*no*/
                            .dplayer-icon.dplayer-play-icon {
                                padding: 7px; /*no*/
                                width: 40px; /*no*/
                                margin: 0 -3px; /*no*/
                            }
                            .dplayer-volume {
                                .dplayer-icon.dplayer-volume-icon {
                                    width: 43px; /*no*/
                                    padding: 7px; /*no*/
                                    margin: 0 -3px; /*no*/
                                }
                                .dplayer-volume-bar-wrap {
                                    margin: 0 10px 0 -5px; /*no*/
                                    .dplayer-volume-bar {
                                        top: 17px; /*no*/
                                        width: 0; /*no*/
                                        height: 3px; /*no*/
                                        .dplayer-volume-bar-inner {
                                            .dplayer-thumb {
                                                right: 5px; /*no*/
                                                margin-top: -4px; /*no*/
                                                margin-right: -10px; /*no*/
                                                height: 11px; /*no*/
                                                width: 11px; /*no*/
                                            }
                                        }
                                    }
                                }
                                [data-balloon-pos="up"]:after {
                                    margin-bottom: 11px; /*no*/
                                }
                                [data-balloon][data-balloon-pos="up"]:after,
                                [data-balloon][data-balloon-pos="up"]:before {
                                    -webkit-transform: translate(
                                        -50%,
                                        10px
                                    ); /*no*/
                                    transform: translate(-50%, 10px); /*no*/
                                }
                            }
                            .dplayer-time {
                                line-height: 38px; /*no*/
                                font-size: 13px; /*no*/
                            }
                        }
                        .dplayer-icons.dplayer-icons-right {
                            right: 20px; /*no*/
                            height: 38px; /*no*/
                            .dplayer-setting {
                                .dplayer-icon.dplayer-setting-icon {
                                    padding: 8px; /*no*/
                                    padding-top: 8.5px; /*no*/
                                    width: 40px; /*no*/
                                }
                                .dplayer-setting-box {
                                    bottom: 50px; /*no*/
                                    width: 150px; /*no*/
                                    border-radius: 2px; /*no*/
                                    padding: 7px 0; /*no*/
                                }
                            }
                            .dplayer-full {
                                .dplayer-icon.dplayer-full-in-icon {
                                    top: -30px; /*no*/
                                    padding: 8px; /*no*/
                                    width: 40px; /*no*/
                                }
                                .dplayer-icon.dplayer-full-icon {
                                    padding: 8px; /*no*/
                                    width: 40px; /*no*/
                                }
                            }
                        }
                        .dplayer-bar-wrap {
                            padding: 5px 0; /*no*/
                            bottom: 33px; /*no*/
                            width: calc(100% - 40px); /*no*/
                            height: 3px; /*no*/
                        }
                    }
                    .dplayer-controller-mask {
                        height: 98px; /*no*/
                    }
                }
            }
            .vic-bg {
                position: absolute;
                bottom: 0;
                left: 0;
                width: 100%;
                height: 100%;
                z-index: 29;
            }
        }
    }
}
</style>
